"use client";

import { Button } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON><PERSON>gle, ChevronLeft, ChevronRight, X } from "lucide-react";
import { motion } from "motion/react";
import Image from "next/image";
import { useState } from "react";

interface ImageGalleryProps {
   images: {
      src: string;
      alt: string;
      category?: string;
      width?: number;
      height?: number;
      displayOrder?: number;
      createdAt?: Date | string;
   }[];
   showCategory?: boolean;
   className?: string;
   sortByDisplayOrder?: boolean;
}

export function ImageGallery({
   images,
   showCategory = false,
   className = "",
   sortByDisplayOrder = false,
}: ImageGalleryProps) {
   const [selectedImage, setSelectedImage] = useState<number | null>(null);
   const [imageErrors, setImageErrors] = useState<Set<number>>(new Set());
   const [imageLoading, setImageLoading] = useState<Set<number>>(new Set());

   const handleImageError = (index: number) => {
      setImageErrors((prev) => new Set(prev).add(index));
      setImageLoading((prev) => {
         const newSet = new Set(prev);
         newSet.delete(index);
         return newSet;
      });
   };

   const handleImageLoad = (index: number) => {
      setImageLoading((prev) => {
         const newSet = new Set(prev);
         newSet.delete(index);
         return newSet;
      });
   };

   const handleImageLoadStart = (index: number) => {
      setImageLoading((prev) => new Set(prev).add(index));
   };

   // Sort images based on display order if requested
   const sortedImages = sortByDisplayOrder
      ? [...images].sort((a, b) => {
           // Primary sort: by display order (ascending)
           const orderA = a.displayOrder ?? Number.MAX_SAFE_INTEGER;
           const orderB = b.displayOrder ?? Number.MAX_SAFE_INTEGER;

           if (orderA !== orderB) {
              return orderA - orderB;
           }

           // Secondary sort: by creation date (newest first) for same display order
           const dateA = a.createdAt ? new Date(a.createdAt).getTime() : 0;
           const dateB = b.createdAt ? new Date(b.createdAt).getTime() : 0;

           return dateB - dateA;
        })
      : images;

   const openLightbox = (index: number) => {
      setSelectedImage(index);
   };

   const closeLightbox = () => {
      setSelectedImage(null);
   };

   const nextImage = () => {
      if (selectedImage !== null) {
         setSelectedImage((selectedImage + 1) % sortedImages.length);
      }
   };

   const prevImage = () => {
      if (selectedImage !== null) {
         setSelectedImage(
            selectedImage === 0 ? sortedImages.length - 1 : selectedImage - 1
         );
      }
   };

   return (
      <>
         {/* Gallery Grid */}
         <div
            className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 ${className}`}
         >
            {sortedImages.map((image, index) => (
               <motion.div
                  key={index}
                  initial={{ opacity: 0 }}
                  whileInView={{ opacity: 1 }}
                  transition={{ duration: 0.6 }}
                  viewport={{ once: true }}
                  className="group relative overflow-hidden rounded-2xl bg-card border border-astral-grey-light hover:shadow-elegant transition-all duration-500 cursor-pointer"
                  onClick={() => !imageErrors.has(index) && openLightbox(index)}
               >
                  <div className="relative h-85 overflow-hidden">
                     {imageErrors.has(index) ? (
                        // Error state for failed images
                        <div className="w-full h-full flex flex-col items-center justify-center bg-muted text-muted-foreground">
                           <AlertTriangle className="w-8 h-8 mb-2" />
                           <span className="text-sm text-center px-4">
                              Failed to load image
                           </span>
                           <span className="text-xs text-center px-4 mt-1 opacity-70">
                              {image.alt}
                           </span>
                        </div>
                     ) : imageLoading.has(index) ? (
                        // Loading state
                        <div className="w-full h-full flex items-center justify-center bg-muted">
                           <div className="flex flex-col items-center gap-2 text-muted-foreground">
                              <div className="w-6 h-6 border-2 border-current border-t-transparent rounded-full animate-spin" />
                              <span className="text-sm">Loading...</span>
                           </div>
                        </div>
                     ) : (
                        <Image
                           src={image.src}
                           alt={image.alt}
                           fill
                           className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                           onError={() => handleImageError(index)}
                           onLoad={() => handleImageLoad(index)}
                           onLoadStart={() => handleImageLoadStart(index)}
                           sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                        />
                     )}
                  </div>
                  {showCategory && image.category && (
                     <div className="p-6">
                        <div className="flex items-center justify-between">
                           <span className="text-sm font-montserrat font-medium text-primary bg-primary/10 px-3 py-1 rounded-full">
                              {image.category}
                           </span>
                        </div>
                     </div>
                  )}
               </motion.div>
               // <div
               //    key={index}
               //    className="relative h-80 overflow-hidden rounded-md cursor-pointer group"
               //    onClick={() => openLightbox(index)}
               // >
               //    <Image
               //       src={image.src}
               //       alt={image.alt}
               //       fill
               //       className="object-cover transition-transform duration-300 group-hover:scale-105"
               //       sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
               //    />
               //    <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300" />
               // </div>
            ))}
         </div>

         {/* Lightbox */}
         {selectedImage !== null && (
            <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
               <div
                  className="fixed inset-0 -z-50 bg-black/90"
                  onClick={closeLightbox}
               ></div>
               <Button
                  variant="ghost"
                  size="icon"
                  className="absolute top-4 right-4 bg-white/20 rounded-full sm:bg-transparent text-white size-12 hover:bg-white/20 z-100"
                  onClick={closeLightbox}
               >
                  <X className="!size:6 sm:!size-8" />
               </Button>

               <Button
                  variant="ghost"
                  size="icon"
                  className="absolute bg-gradient-accent !size-6 rounded-full left-8 top-1/2 -translate-y-1/2 text-white p-6 hover:bg-white/20 z-100"
                  onClick={prevImage}
               >
                  <ChevronLeft className="size-6 flex" />
               </Button>

               <Button
                  variant="ghost"
                  size="icon"
                  className="absolute bg-gradient-accent !size-6 rounded-full right-8 top-1/2 -translate-y-1/2 text-white p-6 hover:bg-white/20 z-100"
                  onClick={nextImage}
               >
                  <ChevronRight className="size-6 flex" />
               </Button>

               <div className="relative max-w-4xl w-[800px] h-[800px] max-h-full">
                  {imageErrors.has(selectedImage) ? (
                     // Error state in lightbox
                     <div className="w-full h-full flex flex-col items-center justify-center bg-black/50 rounded-lg text-white">
                        <AlertTriangle className="w-16 h-16 mb-4" />
                        <h3 className="text-xl font-semibold mb-2">
                           Image Not Available
                        </h3>
                        <p className="text-center text-white/80 max-w-md">
                           This image could not be loaded. It may have been
                           moved or deleted.
                        </p>
                        <p className="text-sm text-white/60 mt-2">
                           {sortedImages[selectedImage].alt}
                        </p>
                     </div>
                  ) : (
                     <Image
                        src={sortedImages[selectedImage].src}
                        alt={sortedImages[selectedImage].alt}
                        fill
                        className="object-contain"
                        onError={() => handleImageError(selectedImage)}
                        priority
                     />
                  )}
               </div>
            </div>
         )}
      </>
   );
}
