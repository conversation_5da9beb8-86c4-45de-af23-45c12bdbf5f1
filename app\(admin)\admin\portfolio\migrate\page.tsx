"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
   Card,
   CardContent,
   CardDescription,
   CardHeader,
   CardTitle,
} from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import {
   forceMigratePortfolioDataAction,
   getMigrationStatusAction,
   migratePortfolioDataAction,
} from "@/lib/actions/portfolio-actions";
import { MigrationResult } from "@/lib/services/portfolio-migration-service";
import {
   AlertCircle,
   ArrowLeft,
   CheckCircle,
   Database,
   Download,
   Info,
   Loader2,
   RefreshCw,
} from "lucide-react";
import Link from "next/link";
import { useEffect, useState } from "react";

interface MigrationState {
   isRunning: boolean;
   isComplete: boolean;
   result: MigrationResult | null;
   error: string | null;
}

interface MigrationStatus {
   hasExistingData: boolean;
   existingServicesCount: number;
   existingImagesCount: number;
   duplicateDetection: {
      duplicateServiceSlugs: string[];
      duplicateImageUrls: string[];
   } | null;
}

export default function PortfolioMigratePage() {
   const [migrationState, setMigrationState] = useState<MigrationState>({
      isRunning: false,
      isComplete: false,
      result: null,
      error: null,
   });

   const [migrationStatus, setMigrationStatus] =
      useState<MigrationStatus | null>(null);
   const [statusLoading, setStatusLoading] = useState(true);
   const [showForceOption, setShowForceOption] = useState(false);

   // Load migration status on component mount
   useEffect(() => {
      loadMigrationStatus();
   }, []);

   const loadMigrationStatus = async () => {
      setStatusLoading(true);
      try {
         const response = await getMigrationStatusAction();
         if (response.success && response.data) {
            setMigrationStatus(response.data);
            setShowForceOption(response.data.hasExistingData);
         }
      } catch (error) {
         console.error("Error loading migration status:", error);
      } finally {
         setStatusLoading(false);
      }
   };

   const handleMigration = async (forceUpdate = false) => {
      setMigrationState({
         isRunning: true,
         isComplete: false,
         result: null,
         error: null,
      });

      try {
         const response = forceUpdate
            ? await forceMigratePortfolioDataAction()
            : await migratePortfolioDataAction();

         if (response.success && response.data) {
            // Check if migration was actually successful or just returned with warnings
            const hasErrors =
               response.data.errors && response.data.errors.length > 0;
            const totalProcessed =
               response.data.servicesCreated +
               (response.data.servicesUpdated || 0) +
               response.data.imagesCreated +
               (response.data.imagesUpdated || 0);

            if (totalProcessed > 0) {
               setMigrationState({
                  isRunning: false,
                  isComplete: true,
                  result: response.data,
                  error: null,
               });
               // Reload status after successful migration
               await loadMigrationStatus();
            } else if (hasErrors) {
               // Migration returned success but with errors and no data processed
               setMigrationState({
                  isRunning: false,
                  isComplete: false,
                  result: response.data,
                  error: response.data.errors.join("; "),
               });
            } else {
               setMigrationState({
                  isRunning: false,
                  isComplete: false,
                  result: response.data,
                  error: "Migration completed but no data was processed",
               });
            }
         } else {
            setMigrationState({
               isRunning: false,
               isComplete: false,
               result: response.data || null,
               error: response.error || "Migration failed with unknown error",
            });
         }
      } catch (error) {
         console.error("Migration error:", error);

         let errorMessage = "An unexpected error occurred during migration";
         if (error instanceof Error) {
            errorMessage = error.message;
         } else if (typeof error === "string") {
            errorMessage = error;
         }

         setMigrationState({
            isRunning: false,
            isComplete: false,
            result: null,
            error: errorMessage,
         });
      }
   };

   const resetMigration = () => {
      setMigrationState({
         isRunning: false,
         isComplete: false,
         result: null,
         error: null,
      });
      loadMigrationStatus();
   };

   const downloadMigrationReport = () => {
      if (!migrationState.result) return;

      // Generate report content
      const timestamp = new Date().toISOString();
      const totalProcessed =
         migrationState.result.servicesCreated +
         (migrationState.result.servicesUpdated || 0) +
         migrationState.result.imagesCreated +
         (migrationState.result.imagesUpdated || 0);

      const reportContent = `Portfolio Migration Report
Generated: ${new Date().toLocaleString()}

MIGRATION SUMMARY:
- Total Items Processed: ${totalProcessed}
- Services Created: ${migrationState.result.servicesCreated}
- Services Updated: ${migrationState.result.servicesUpdated || 0}
- Images Created: ${migrationState.result.imagesCreated}
- Images Updated: ${migrationState.result.imagesUpdated || 0}
- Duplicates Skipped: ${migrationState.result.duplicatesSkipped}
- Warnings: ${migrationState.result.warnings?.length || 0}
- Errors: ${migrationState.result.errors?.length || 0}

STATUS: ${totalProcessed > 0 ? "SUCCESS" : "FAILED"}

${
   migrationState.result.serviceMapping &&
   migrationState.result.serviceMapping.size > 0
      ? `SERVICE MAPPING:
Successfully mapped ${migrationState.result.serviceMapping.size} categories to portfolio services

`
      : ""
}${
         migrationState.result.warnings &&
         migrationState.result.warnings.length > 0
            ? `WARNINGS (${migrationState.result.warnings.length}):
${migrationState.result.warnings
   .map((warning, index) => `${index + 1}. ${warning}`)
   .join("\n")}

`
            : ""
      }${
         migrationState.result.errors && migrationState.result.errors.length > 0
            ? `ERRORS (${migrationState.result.errors.length}):
${migrationState.result.errors
   .map((error, index) => `${index + 1}. ${error}`)
   .join("\n")}

`
            : ""
      }${
         migrationState.result.duplicatesSkipped > 0
            ? `DUPLICATE HANDLING:
${migrationState.result.duplicatesSkipped} duplicate items were safely skipped to preserve existing data.
Use "Force Update" migration to overwrite existing data if needed.

`
            : ""
      }Report generated at: ${timestamp}`;

      // Create and download file
      const blob = new Blob([reportContent], { type: "text/plain" });
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `portfolio-migration-report-${
         new Date().toISOString().split("T")[0]
      }.txt`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
   };

   return (
      <div className="p-8 space-y-8">
         {/* Breadcrumb Navigation */}
         <div className="flex items-center space-x-2 text-sm text-muted-foreground">
            <Link
               href="/admin/portfolio"
               className="flex items-center hover:text-foreground transition-colors"
            >
               <ArrowLeft className="w-4 h-4 mr-1" />
               Portfolio Services
            </Link>
            <span>/</span>
            <span className="text-foreground">Data Migration</span>
         </div>

         {/* Header */}
         <div className="flex items-center space-x-4">
            <div>
               <h1 className="text-2xl sm:text-3xl font-bold text-foreground">
                  Portfolio Data Migration
               </h1>
               <p className="text-muted-foreground">
                  Migrate portfolio data from static files to database
               </p>
            </div>
         </div>

         {/* Migration Card */}
         <Card className="border-border/30">
            <CardHeader>
               <CardTitle className="flex items-center gap-2">
                  <Database className="w-5 h-5" />
                  Portfolio Migration
               </CardTitle>
               <CardDescription>
                  This will migrate your existing portfolio data from the static
                  portfolio.ts file to the database. This operation should only
                  be run once.
               </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
               {/* Migration Status */}
               {statusLoading ? (
                  <div className="flex items-center gap-3">
                     <Loader2 className="w-4 h-4 animate-spin" />
                     <span className="text-sm text-muted-foreground">
                        Checking migration status...
                     </span>
                  </div>
               ) : (
                  migrationStatus && (
                     <div className="space-y-4">
                        {/* Current Status */}
                        <div className="p-4 bg-gray-50 dark:bg-gray-950/20 border border-gray-200 dark:border-gray-800 rounded-lg">
                           <div className="flex items-start gap-3">
                              <Info className="w-5 h-5 text-gray-600 dark:text-gray-400 mt-0.5 flex-shrink-0" />
                              <div className="text-sm">
                                 <p className="font-medium text-gray-800 dark:text-gray-200 mb-2">
                                    Current Database Status:
                                 </p>
                                 <div className="grid grid-cols-2 gap-4">
                                    <div>
                                       <span className="text-gray-600 dark:text-gray-400">
                                          Services:
                                       </span>
                                       <span className="ml-2 font-medium text-gray-800 dark:text-gray-200">
                                          {
                                             migrationStatus.existingServicesCount
                                          }
                                       </span>
                                    </div>
                                    <div>
                                       <span className="text-gray-600 dark:text-gray-400">
                                          Images:
                                       </span>
                                       <span className="ml-2 font-medium text-gray-800 dark:text-gray-200">
                                          {migrationStatus.existingImagesCount}
                                       </span>
                                    </div>
                                 </div>
                              </div>
                           </div>
                        </div>

                        {/* Duplicate Detection Results */}
                        {migrationStatus.duplicateDetection &&
                           (migrationStatus.duplicateDetection
                              .duplicateServiceSlugs.length > 0 ||
                              migrationStatus.duplicateDetection
                                 .duplicateImageUrls.length > 0) && (
                              <div className="p-4 bg-yellow-50 dark:bg-yellow-950/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                                 <div className="flex items-start gap-3">
                                    <AlertCircle className="w-5 h-5 text-yellow-600 dark:text-yellow-400 mt-0.5 flex-shrink-0" />
                                    <div className="text-sm">
                                       <p className="font-medium text-yellow-800 dark:text-yellow-200 mb-2">
                                          Duplicate Data Detected:
                                       </p>
                                       {migrationStatus.duplicateDetection
                                          .duplicateServiceSlugs.length > 0 && (
                                          <p className="text-yellow-700 dark:text-yellow-300 mb-1">
                                             •{" "}
                                             {
                                                migrationStatus
                                                   .duplicateDetection
                                                   .duplicateServiceSlugs.length
                                             }{" "}
                                             duplicate service(s)
                                          </p>
                                       )}
                                       {migrationStatus.duplicateDetection
                                          .duplicateImageUrls.length > 0 && (
                                          <p className="text-yellow-700 dark:text-yellow-300">
                                             •{" "}
                                             {
                                                migrationStatus
                                                   .duplicateDetection
                                                   .duplicateImageUrls.length
                                             }{" "}
                                             duplicate image(s)
                                          </p>
                                       )}
                                    </div>
                                 </div>
                              </div>
                           )}
                     </div>
                  )
               )}

               {/* Migration Actions */}
               {!migrationState.isRunning &&
                  !migrationState.isComplete &&
                  !migrationState.error && (
                     <div className="space-y-4">
                        <div className="p-4 bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                           <div className="flex items-start gap-3">
                              <AlertCircle className="w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
                              <div className="text-sm text-blue-800 dark:text-blue-200">
                                 <p className="font-medium mb-1">
                                    Migration Options:
                                 </p>
                                 <ul className="list-disc list-inside space-y-1">
                                    <li>
                                       Standard migration will skip existing
                                       data to prevent duplicates
                                    </li>
                                    <li>
                                       Force migration will update existing data
                                       with new values
                                    </li>
                                    <li>
                                       Migration can be run multiple times
                                       safely
                                    </li>
                                 </ul>
                              </div>
                           </div>
                        </div>

                        <div className="flex gap-3">
                           <Button
                              onClick={() => handleMigration(false)}
                              size="lg"
                              className="flex-1 sm:flex-none"
                           >
                              <Database className="w-4 h-4 mr-2" />
                              Start Migration
                           </Button>

                           {showForceOption && (
                              <Button
                                 onClick={() => handleMigration(true)}
                                 variant="outline"
                                 size="lg"
                                 className="flex-1 sm:flex-none"
                              >
                                 <RefreshCw className="w-4 h-4 mr-2" />
                                 Force Update
                              </Button>
                           )}
                        </div>
                     </div>
                  )}

               {/* Progress Indicator */}
               {migrationState.isRunning && (
                  <div className="space-y-4">
                     <div className="flex items-center gap-3">
                        <Loader2 className="w-5 h-5 animate-spin text-blue-600" />
                        <span className="text-sm font-medium">
                           Migration in progress...
                        </span>
                     </div>
                     <Progress value={50} className="w-full" />
                     <p className="text-sm text-muted-foreground">
                        Please wait while we migrate your portfolio data. This
                        may take a few moments.
                     </p>
                  </div>
               )}

               {/* Success Results */}
               {migrationState.isComplete && migrationState.result && (
                  <div className="space-y-6">
                     {/* Success Header */}
                     <div className="flex items-center gap-3 text-green-600 dark:text-green-400">
                        <CheckCircle className="w-5 h-5" />
                        <span className="font-medium">
                           Migration completed successfully!
                        </span>
                     </div>

                     {/* Migration Summary */}
                     <div className="p-4 bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800 rounded-lg">
                        <h3 className="font-medium text-green-800 dark:text-green-200 mb-3">
                           Migration Summary
                        </h3>
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
                           <div className="space-y-2">
                              <div className="flex justify-between">
                                 <span className="text-green-700 dark:text-green-300">
                                    Total Items Processed:
                                 </span>
                                 <span className="font-medium text-green-800 dark:text-green-200">
                                    {migrationState.result.servicesCreated +
                                       (migrationState.result.servicesUpdated ||
                                          0) +
                                       migrationState.result.imagesCreated +
                                       (migrationState.result.imagesUpdated ||
                                          0)}
                                 </span>
                              </div>
                              <div className="flex justify-between">
                                 <span className="text-green-700 dark:text-green-300">
                                    Services Processed:
                                 </span>
                                 <span className="font-medium text-green-800 dark:text-green-200">
                                    {migrationState.result.servicesCreated +
                                       (migrationState.result.servicesUpdated ||
                                          0)}
                                 </span>
                              </div>
                              <div className="flex justify-between">
                                 <span className="text-green-700 dark:text-green-300">
                                    Images Processed:
                                 </span>
                                 <span className="font-medium text-green-800 dark:text-green-200">
                                    {migrationState.result.imagesCreated +
                                       (migrationState.result.imagesUpdated ||
                                          0)}
                                 </span>
                              </div>
                           </div>
                           <div className="space-y-2">
                              <div className="flex justify-between">
                                 <span className="text-green-700 dark:text-green-300">
                                    Duplicates Handled:
                                 </span>
                                 <span className="font-medium text-green-800 dark:text-green-200">
                                    {migrationState.result.duplicatesSkipped}
                                 </span>
                              </div>
                              <div className="flex justify-between">
                                 <span className="text-green-700 dark:text-green-300">
                                    Warnings:
                                 </span>
                                 <span className="font-medium text-green-800 dark:text-green-200">
                                    {migrationState.result.warnings?.length ||
                                       0}
                                 </span>
                              </div>
                              <div className="flex justify-between">
                                 <span className="text-green-700 dark:text-green-300">
                                    Errors:
                                 </span>
                                 <span className="font-medium text-green-800 dark:text-green-200">
                                    {migrationState.result.errors?.length || 0}
                                 </span>
                              </div>
                           </div>
                        </div>
                     </div>

                     {/* Detailed Results Grid */}

                     <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
                        <div className="p-4 bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800 rounded-lg">
                           <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                              {migrationState.result.servicesCreated}
                           </div>
                           <div className="text-sm text-green-800 dark:text-green-200">
                              Services Created
                           </div>
                        </div>
                        <div className="p-4 bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                           <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                              {migrationState.result.servicesUpdated || 0}
                           </div>
                           <div className="text-sm text-blue-800 dark:text-blue-200">
                              Services Updated
                           </div>
                        </div>
                        <div className="p-4 bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800 rounded-lg">
                           <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                              {migrationState.result.imagesCreated}
                           </div>
                           <div className="text-sm text-green-800 dark:text-green-200">
                              Images Created
                           </div>
                        </div>
                        <div className="p-4 bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                           <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                              {migrationState.result.imagesUpdated || 0}
                           </div>
                           <div className="text-sm text-blue-800 dark:text-blue-200">
                              Images Updated
                           </div>
                        </div>
                     </div>

                     {/* Service Mapping Details */}
                     {migrationState.result.serviceMapping &&
                        migrationState.result.serviceMapping.size > 0 && (
                           <div className="p-4 bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                              <div className="flex items-start gap-3">
                                 <Info className="w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
                                 <div>
                                    <p className="font-medium text-blue-800 dark:text-blue-200 mb-2">
                                       Service Mapping Created:
                                    </p>
                                    <div className="text-sm text-blue-700 dark:text-blue-300">
                                       <p>
                                          Successfully mapped{" "}
                                          {
                                             migrationState.result
                                                .serviceMapping.size
                                          }{" "}
                                          categories to portfolio services
                                       </p>
                                       <p className="mt-1 text-xs">
                                          Categories from static data have been
                                          converted to database services with
                                          proper relationships
                                       </p>
                                    </div>
                                 </div>
                              </div>
                           </div>
                        )}

                     {/* Duplicates Information */}
                     {migrationState.result.duplicatesSkipped > 0 && (
                        <div className="p-4 bg-gray-50 dark:bg-gray-950/20 border border-gray-200 dark:border-gray-800 rounded-lg">
                           <div className="flex items-start gap-3">
                              <Info className="w-5 h-5 text-gray-600 dark:text-gray-400 mt-0.5 flex-shrink-0" />
                              <div>
                                 <p className="font-medium text-gray-800 dark:text-gray-200 mb-2">
                                    Duplicate Handling Report:
                                 </p>
                                 <div className="text-sm text-gray-700 dark:text-gray-300 space-y-1">
                                    <p>
                                       <span className="font-medium">
                                          {
                                             migrationState.result
                                                .duplicatesSkipped
                                          }
                                       </span>{" "}
                                       duplicate items were safely skipped
                                    </p>
                                    <p className="text-xs text-gray-600 dark:text-gray-400">
                                       Existing data was preserved to prevent
                                       conflicts. Use &quot;Force Update&quot;
                                       to overwrite existing data.
                                    </p>
                                 </div>
                              </div>
                           </div>
                        </div>
                     )}

                     {/* Warnings Section */}
                     {migrationState.result.warnings &&
                        migrationState.result.warnings.length > 0 && (
                           <div className="p-4 bg-yellow-50 dark:bg-yellow-950/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                              <div className="flex items-start gap-3">
                                 <AlertCircle className="w-5 h-5 text-yellow-600 dark:text-yellow-400 mt-0.5 flex-shrink-0" />
                                 <div className="w-full">
                                    <p className="font-medium text-yellow-800 dark:text-yellow-200 mb-3">
                                       Migration Warnings (
                                       {migrationState.result.warnings.length}):
                                    </p>
                                    <div className="space-y-2">
                                       {migrationState.result.warnings.map(
                                          (warning: string, index: number) => (
                                             <div
                                                key={index}
                                                className="p-2 bg-yellow-100 dark:bg-yellow-900/30 rounded border-l-2 border-yellow-400"
                                             >
                                                <p className="text-sm text-yellow-800 dark:text-yellow-200">
                                                   {warning}
                                                </p>
                                             </div>
                                          )
                                       )}
                                    </div>
                                    <p className="text-xs text-yellow-700 dark:text-yellow-300 mt-2">
                                       Warnings indicate non-critical issues
                                       that didn&apos;t prevent migration
                                       completion.
                                    </p>
                                 </div>
                              </div>
                           </div>
                        )}

                     {/* Errors Section */}
                     {migrationState.result.errors &&
                        migrationState.result.errors.length > 0 && (
                           <div className="p-4 bg-orange-50 dark:bg-orange-950/20 border border-orange-200 dark:border-orange-800 rounded-lg">
                              <div className="flex items-start gap-3">
                                 <AlertCircle className="w-5 h-5 text-orange-600 dark:text-orange-400 mt-0.5 flex-shrink-0" />
                                 <div className="w-full">
                                    <p className="font-medium text-orange-800 dark:text-orange-200 mb-3">
                                       Migration Errors (
                                       {migrationState.result.errors.length}):
                                    </p>
                                    <div className="space-y-2">
                                       {migrationState.result.errors.map(
                                          (error, index) => (
                                             <div
                                                key={index}
                                                className="p-2 bg-orange-100 dark:bg-orange-900/30 rounded border-l-2 border-orange-400"
                                             >
                                                <p className="text-sm text-orange-800 dark:text-orange-200">
                                                   {error}
                                                </p>
                                             </div>
                                          )
                                       )}
                                    </div>
                                    <p className="text-xs text-orange-700 dark:text-orange-300 mt-2">
                                       Errors indicate issues with specific
                                       items but didn&apos;t prevent overall
                                       migration success.
                                    </p>
                                 </div>
                              </div>
                           </div>
                        )}

                     {/* Success Actions */}
                     <div className="flex flex-wrap gap-3">
                        <Button
                           onClick={downloadMigrationReport}
                           variant="outline"
                           size="sm"
                        >
                           <Download className="w-4 h-4 mr-2" />
                           Download Report
                        </Button>
                        <Button onClick={resetMigration} variant="outline">
                           Run Another Migration
                        </Button>
                        <Button asChild>
                           <Link href="/admin/portfolio">View Portfolio</Link>
                        </Button>
                     </div>
                  </div>
               )}

               {/* Error State */}
               {migrationState.error && (
                  <div className="space-y-4">
                     <div className="p-4 bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800 rounded-lg">
                        <div className="flex items-start gap-3">
                           <AlertCircle className="w-5 h-5 text-red-600 dark:text-red-400 mt-0.5 flex-shrink-0" />
                           <div>
                              <p className="font-medium text-red-800 dark:text-red-200 mb-1">
                                 Migration Failed
                              </p>
                              <p className="text-sm text-red-700 dark:text-red-300">
                                 {migrationState.error}
                              </p>
                           </div>
                        </div>
                     </div>

                     {/* Show partial results if available */}
                     {migrationState.result && (
                        <div className="space-y-4">
                           <div className="p-4 bg-gray-50 dark:bg-gray-950/20 border border-gray-200 dark:border-gray-800 rounded-lg">
                              <p className="text-sm font-medium text-gray-800 dark:text-gray-200 mb-3">
                                 Partial Migration Results:
                              </p>
                              <div className="grid grid-cols-2 sm:grid-cols-4 gap-3 mb-3">
                                 <div className="p-3 bg-white dark:bg-gray-900/50 border border-gray-200 dark:border-gray-700 rounded-lg">
                                    <div className="text-lg font-bold text-gray-600 dark:text-gray-400">
                                       {migrationState.result.servicesCreated}
                                    </div>
                                    <div className="text-xs text-gray-800 dark:text-gray-200">
                                       Services Created
                                    </div>
                                 </div>
                                 <div className="p-3 bg-white dark:bg-gray-900/50 border border-gray-200 dark:border-gray-700 rounded-lg">
                                    <div className="text-lg font-bold text-gray-600 dark:text-gray-400">
                                       {migrationState.result.servicesUpdated ||
                                          0}
                                    </div>
                                    <div className="text-xs text-gray-800 dark:text-gray-200">
                                       Services Updated
                                    </div>
                                 </div>
                                 <div className="p-3 bg-white dark:bg-gray-900/50 border border-gray-200 dark:border-gray-700 rounded-lg">
                                    <div className="text-lg font-bold text-gray-600 dark:text-gray-400">
                                       {migrationState.result.imagesCreated}
                                    </div>
                                    <div className="text-xs text-gray-800 dark:text-gray-200">
                                       Images Created
                                    </div>
                                 </div>
                                 <div className="p-3 bg-white dark:bg-gray-900/50 border border-gray-200 dark:border-gray-700 rounded-lg">
                                    <div className="text-lg font-bold text-gray-600 dark:text-gray-400">
                                       {migrationState.result.imagesUpdated ||
                                          0}
                                    </div>
                                    <div className="text-xs text-gray-800 dark:text-gray-200">
                                       Images Updated
                                    </div>
                                 </div>
                              </div>
                              <div className="text-xs text-gray-600 dark:text-gray-400">
                                 Total processed:{" "}
                                 {migrationState.result.servicesCreated +
                                    (migrationState.result.servicesUpdated ||
                                       0) +
                                    migrationState.result.imagesCreated +
                                    (migrationState.result.imagesUpdated ||
                                       0)}{" "}
                                 items
                              </div>
                           </div>

                           {/* Show errors from partial results */}
                           {migrationState.result.errors &&
                              migrationState.result.errors.length > 0 && (
                                 <div className="p-4 bg-orange-50 dark:bg-orange-950/20 border border-orange-200 dark:border-orange-800 rounded-lg">
                                    <p className="font-medium text-orange-800 dark:text-orange-200 mb-2">
                                       Specific Errors (
                                       {migrationState.result.errors.length}):
                                    </p>
                                    <div className="space-y-1 max-h-32 overflow-y-auto">
                                       {migrationState.result.errors
                                          .slice(0, 5)
                                          .map((error, index) => (
                                             <div
                                                key={index}
                                                className="text-sm text-orange-700 dark:text-orange-300 p-1 bg-orange-100 dark:bg-orange-900/30 rounded"
                                             >
                                                {error}
                                             </div>
                                          ))}
                                       {migrationState.result.errors.length >
                                          5 && (
                                          <p className="text-xs text-orange-600 dark:text-orange-400">
                                             ... and{" "}
                                             {migrationState.result.errors
                                                .length - 5}{" "}
                                             more errors
                                          </p>
                                       )}
                                    </div>
                                 </div>
                              )}

                           {/* Show warnings from partial results */}
                           {migrationState.result.warnings &&
                              migrationState.result.warnings.length > 0 && (
                                 <div className="p-4 bg-yellow-50 dark:bg-yellow-950/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                                    <p className="font-medium text-yellow-800 dark:text-yellow-200 mb-2">
                                       Warnings (
                                       {migrationState.result.warnings.length}):
                                    </p>
                                    <div className="space-y-1 max-h-32 overflow-y-auto">
                                       {migrationState.result.warnings
                                          .slice(0, 3)
                                          .map((warning, index) => (
                                             <div
                                                key={index}
                                                className="text-sm text-yellow-700 dark:text-yellow-300 p-1 bg-yellow-100 dark:bg-yellow-900/30 rounded"
                                             >
                                                {warning}
                                             </div>
                                          ))}
                                       {migrationState.result.warnings.length >
                                          3 && (
                                          <p className="text-xs text-yellow-600 dark:text-yellow-400">
                                             ... and{" "}
                                             {migrationState.result.warnings
                                                .length - 3}{" "}
                                             more warnings
                                          </p>
                                       )}
                                    </div>
                                 </div>
                              )}
                        </div>
                     )}

                     <div className="flex gap-3">
                        {migrationState.result && (
                           <Button
                              onClick={downloadMigrationReport}
                              variant="outline"
                              size="sm"
                           >
                              <Download className="w-4 h-4 mr-2" />
                              Download Report
                           </Button>
                        )}
                        <Button onClick={resetMigration} variant="outline">
                           Try Again
                        </Button>
                     </div>
                  </div>
               )}
            </CardContent>
         </Card>
      </div>
   );
}
