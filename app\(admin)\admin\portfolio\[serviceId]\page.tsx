"use client";

import CoverImageSelector from "@/components/admin/portfolio/cover-image-selector";
import PortfolioGallery from "@/components/admin/portfolio/portfolio-gallery";
import PortfolioUploadDialog from "@/components/admin/portfolio/portfolio-upload-dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
   Card,
   CardContent,
   CardDescription,
   CardHeader,
   CardTitle,
} from "@/components/ui/card";
import {
   Dialog,
   DialogContent,
   DialogDescription,
   DialogFooter,
   DialogHeader,
   DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Skeleton } from "@/components/ui/skeleton";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import {
   useDeletePortfolioImage,
   usePortfolioImagesByService,
   usePortfolioService,
   useUpdatePortfolioService,
} from "@/lib/hooks/use-portfolio";
import { zodResolver } from "@hookform/resolvers/zod";
import {
   ArrowLeft,
   Edit,
   Image as ImageIcon,
   Loader2,
   Save,
   Trash2,
   Upload,
   X,
} from "lucide-react";
import Image from "next/image";
import { useParams, useRouter } from "next/navigation";
import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

const serviceFormSchema = z.object({
   name: z
      .string()
      .min(1, "Service name is required")
      .max(100, "Service name must be less than 100 characters")
      .trim(),
   description: z
      .string()
      .max(500, "Description must be less than 500 characters"),
   coverImageUrl: z
      .string()
      .optional()
      .or(z.literal(""))
      .refine((val) => {
         if (!val) return true; // Empty string is valid
         // Allow full URLs (http/https) or relative paths starting with /
         return /^(https?:\/\/|\/)/i.test(val);
      }, "Cover image must be a valid URL or relative path starting with /")
      .refine(
         (val) => !val || !/^(javascript|data|vbscript):/i.test(val),
         "Invalid URL protocol"
      ),
   isActive: z.boolean(),
   displayOrder: z
      .number()
      .int("Display order must be a whole number")
      .min(0, "Display order cannot be negative"),
});

type ServiceFormData = z.infer<typeof serviceFormSchema>;

export default function ServiceDetailPage() {
   const params = useParams();
   const router = useRouter();
   const serviceId = params.serviceId as string;

   const [isEditing, setIsEditing] = useState(false);
   const [isUploadDialogOpen, setIsUploadDialogOpen] = useState(false);
   const [imageToDelete, setImageToDelete] = useState<{
      id: string;
      name: string;
   } | null>(null);

   const {
      data: service,
      isLoading: serviceLoading,
      error: serviceError,
   } = usePortfolioService(serviceId);

   const {
      data: imagesData,
      isLoading: imagesLoading,
      error: imagesError,
   } = usePortfolioImagesByService(serviceId);

   const updateServiceMutation = useUpdatePortfolioService();
   const deleteImageMutation = useDeletePortfolioImage();

   const form = useForm<ServiceFormData>({
      resolver: zodResolver(serviceFormSchema),
      defaultValues: {
         name: "",
         description: "",
         coverImageUrl: "",
         isActive: true,
         displayOrder: 0,
      },
      mode: "onChange",
   });

   const {
      register,
      handleSubmit,
      formState: { errors, isDirty },
      reset,
      watch,
      setValue,
   } = form;

   console.log("CoverImageUrl", watch("coverImageUrl"));

   const isActive = watch("isActive");
   const coverImageUrl = watch("coverImageUrl");

   // Update form when service data loads
   React.useEffect(() => {
      if (service) {
         reset({
            name: service.name,
            description: service.description || "",
            coverImageUrl: service.coverImageUrl || "",
            isActive: service.isActive,
            displayOrder: service.displayOrder,
         });
      }
   }, [service, reset]);

   const onSubmit = async (data: ServiceFormData) => {
      try {
         const formData = new FormData();
         formData.append("name", data.name);
         formData.append("description", data.description);
         formData.append("coverImageUrl", data.coverImageUrl || "");
         formData.append("isActive", data.isActive.toString());
         formData.append("displayOrder", data.displayOrder.toString());

         await updateServiceMutation.mutateAsync({
            id: serviceId,
            formData,
         });

         setIsEditing(false);
      } catch (error) {
         console.error("Failed to update service:", error);
      }
   };

   const handleDeleteImage = async () => {
      if (!imageToDelete) return;

      try {
         await deleteImageMutation.mutateAsync(imageToDelete.id);
         setImageToDelete(null);
      } catch (error) {
         console.error("Failed to delete image:", error);
      }
   };

   const handleImageAction = (imageId: string, action: string) => {
      const image = images.find((img) => img._id?.toString() === imageId);

      if (action === "delete" && image) {
         setImageToDelete({
            id: imageId,
            name: image.name,
         });
      }
   };

   if (serviceError) {
      return (
         <div className="p-8">
            <div className="text-center text-destructive">
               <p>Failed to load portfolio service. Please try again.</p>
               <Button
                  variant="outline"
                  className="mt-4"
                  onClick={() => router.push("/admin/portfolio")}
               >
                  <ArrowLeft className="w-4 h-4" />
                  Back to Portfolio
               </Button>
            </div>
         </div>
      );
   }

   const images = imagesData?.data || [];

   return (
      <div className="p-8 space-y-8">
         {/* Header */}
         <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
               <div>
                  {serviceLoading ? (
                     <div className="space-y-2">
                        <Skeleton className="h-8 w-64" />
                        <Skeleton className="h-4 w-48" />
                     </div>
                  ) : (
                     <>
                        <h1 className="text-2xl sm:text-3xl font-bold text-foreground">
                           {service?.name}
                        </h1>
                        <p className="text-muted-foreground">
                           {service?.imageCount || 0} images •{" "}
                           {service?.isActive ? "Active" : "Inactive"}
                        </p>
                     </>
                  )}
               </div>
            </div>

            <div className="flex items-center space-x-3">
               <Button
                  variant="outline"
                  onClick={() => router.push("/admin/portfolio")}
               >
                  <ArrowLeft className="w-4 h-4 mr-1" />
                  Back to Portfolio
               </Button>
               <Button
                  variant="outline"
                  onClick={() => setIsEditing(!isEditing)}
                  disabled={serviceLoading}
               >
                  {isEditing ? (
                     <X className="w-4 h-4" />
                  ) : (
                     <Edit className="w-4 h-4 mr-1" />
                  )}
                  {isEditing ? "Cancel" : "Edit Service"}
               </Button>
               <Button
                  className="bg-gradient-accent hover:opacity-90"
                  onClick={() => setIsUploadDialogOpen(true)}
               >
                  <Upload className="w-4 h-4" />
                  Upload Images
               </Button>
            </div>
         </div>

         {/* Service Information */}
         <Card className="border-border/30">
            <CardContent>
               {serviceLoading ? (
                  <div className="space-y-4">
                     <Skeleton className="h-10 w-full" />
                     <Skeleton className="h-20 w-full" />
                     <div className="flex space-x-4">
                        <Skeleton className="h-10 w-32" />
                        <Skeleton className="h-6 w-20" />
                     </div>
                  </div>
               ) : isEditing ? (
                  <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                     <div className="space-y-3">
                        <Label htmlFor="name">Service Name *</Label>
                        <Input
                           id="name"
                           placeholder="e.g., Wedding Photography"
                           {...register("name")}
                           className={errors.name ? "border-destructive" : ""}
                        />
                        {errors.name && (
                           <p className="text-sm text-destructive">
                              {errors.name.message}
                           </p>
                        )}
                     </div>

                     <div className="space-y-3">
                        <Label htmlFor="description">Description</Label>
                        <Textarea
                           id="description"
                           placeholder="Enter service description (optional)"
                           rows={3}
                           {...register("description")}
                           className={
                              errors.description ? "border-destructive" : ""
                           }
                        />
                        {errors.description && (
                           <p className="text-sm text-destructive">
                              {errors.description.message}
                           </p>
                        )}
                     </div>

                     <CoverImageSelector
                        serviceId={serviceId}
                        currentCoverImageUrl={coverImageUrl}
                        onCoverImageSelect={(imageUrl) =>
                           setValue("coverImageUrl", imageUrl, {
                              shouldDirty: true,
                           })
                        }
                        onCoverImageRemove={() =>
                           setValue("coverImageUrl", "", { shouldDirty: true })
                        }
                        disabled={updateServiceMutation.isPending}
                     />
                     {errors.coverImageUrl && (
                        <p className="text-sm text-destructive">
                           {errors.coverImageUrl.message}
                        </p>
                     )}

                     <div className="flex items-center space-x-2">
                        <Switch
                           id="isActive"
                           checked={isActive}
                           onCheckedChange={(checked) =>
                              setValue("isActive", checked as boolean, {
                                 shouldDirty: true,
                              })
                           }
                        />
                        <Label
                           htmlFor="isActive"
                           className="text-sm font-normal"
                        >
                           Active service
                        </Label>
                     </div>

                     <div className="flex items-center space-x-3">
                        <Button
                           type="submit"
                           disabled={
                              updateServiceMutation.isPending || !isDirty
                           }
                           className="bg-gradient-accent hover:opacity-90"
                        >
                           {updateServiceMutation.isPending ? (
                              <>
                                 <Loader2 className="w-4 h-4 animate-spin" />
                                 Saving...
                              </>
                           ) : (
                              <>
                                 <Save className="w-4 h-4" />
                                 Save Changes
                              </>
                           )}
                        </Button>
                        <Button
                           type="button"
                           variant="outline"
                           onClick={() => {
                              setIsEditing(false);
                              reset();
                           }}
                        >
                           Cancel
                        </Button>
                     </div>
                  </form>
               ) : (
                  <div className="space-y-4">
                     <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                           <Label className="text-sm font-medium text-muted-foreground">
                              Service Name
                           </Label>
                           <p className="text-foreground font-medium">
                              {service?.name}
                           </p>
                        </div>
                        <div>
                           <Label className="text-sm font-medium text-muted-foreground">
                              Status
                           </Label>
                           <div className="flex items-center space-x-2 mt-1">
                              <span
                                 className={`px-2 py-1 rounded-full text-xs ${
                                    service?.isActive
                                       ? "bg-green-800 text-green-50"
                                       : "bg-gray-100 text-gray-800"
                                 }`}
                              >
                                 {service?.isActive ? "Active" : "Inactive"}
                              </span>
                           </div>
                        </div>
                     </div>

                     {service?.description && (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                           <div>
                              <Label className="text-sm font-medium text-muted-foreground">
                                 Description
                              </Label>
                              <p className="text-foreground">
                                 {service.description}
                              </p>
                           </div>
                        </div>
                     )}

                     {service?.coverImageUrl && (
                        <div>
                           <Label className="text-sm font-medium text-muted-foreground">
                              Cover Image
                           </Label>
                           <div className="relative mt-2 aspect-square h-32 bg-astral-grey/50 rounded-lg overflow-hidden border border-border/30">
                              <Image
                                 src={service.coverImageUrl}
                                 alt="Cover image"
                                 fill
                                 className="w-full h-full object-cover"
                              />
                           </div>
                        </div>
                     )}
                  </div>
               )}
            </CardContent>
         </Card>

         {/* Images Gallery */}
         <Card className="border-border/30">
            <CardHeader>
               <CardTitle className="flex items-center justify-between">
                  <span className="flex items-center">
                     <ImageIcon className="w-5 h-5 mr-2" />
                     Portfolio Images ({images.length})
                  </span>
                  <Button
                     variant="outline"
                     className="bg-gradient-accent hover:opacity-90 text-white border-none"
                     onClick={() => setIsUploadDialogOpen(true)}
                  >
                     <Upload className="w-4 h-4" />
                     Add Images
                  </Button>
               </CardTitle>
               <CardDescription>
                  Manage images for this portfolio service
               </CardDescription>
            </CardHeader>
            <CardContent>
               {imagesLoading ? (
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                     {Array.from({ length: 8 }).map((_, i) => (
                        <div key={i} className="aspect-square">
                           <Skeleton className="w-full h-full rounded-lg" />
                        </div>
                     ))}
                  </div>
               ) : imagesError ? (
                  <div className="text-center text-destructive py-8">
                     <p>Failed to load images. Please try again.</p>
                  </div>
               ) : images.length === 0 ? (
                  <div className="text-center py-12">
                     <ImageIcon className="w-16 h-16 mx-auto mb-4 text-muted-foreground opacity-50" />
                     <h3 className="text-lg font-medium mb-2">No images yet</h3>
                     <p className="text-sm text-muted-foreground mb-4">
                        Upload images to showcase this portfolio service
                     </p>
                     <Button
                        className="bg-gradient-accent hover:opacity-90"
                        onClick={() => setIsUploadDialogOpen(true)}
                     >
                        <Upload className="w-4 h-4" />
                        Upload First Image
                     </Button>
                  </div>
               ) : (
                  <PortfolioGallery
                     images={images}
                     serviceId={serviceId}
                     serviceName={service?.name || ""}
                     onImageAction={handleImageAction}
                     showActions={true}
                     enableBulkOperations={true}
                     columns={4}
                  />
               )}
            </CardContent>
         </Card>

         {/* Portfolio Upload Dialog */}
         <PortfolioUploadDialog
            open={isUploadDialogOpen}
            onOpenChange={setIsUploadDialogOpen}
            serviceId={serviceId}
            onUploadComplete={() => {
               // Refresh images data after upload
               // The query will automatically refetch due to cache invalidation
            }}
            title={`Upload Images to ${service?.name || "Portfolio Service"}`}
            subtitle="Add new images to this portfolio service"
         />

         {/* Delete Image Confirmation */}
         {imageToDelete && (
            <Dialog
               open={!!imageToDelete}
               onOpenChange={() => setImageToDelete(null)}
            >
               <DialogContent>
                  <DialogHeader>
                     <DialogTitle>Delete Image</DialogTitle>
                     <DialogDescription>
                        Are you sure you want to delete &quot;
                        {imageToDelete.name}&quot;? This action cannot be
                        undone.
                     </DialogDescription>
                  </DialogHeader>
                  <DialogFooter>
                     <Button
                        variant="outline"
                        onClick={() => setImageToDelete(null)}
                        disabled={deleteImageMutation.isPending}
                     >
                        Cancel
                     </Button>
                     <Button
                        variant="destructive"
                        onClick={handleDeleteImage}
                        disabled={deleteImageMutation.isPending}
                     >
                        {deleteImageMutation.isPending ? (
                           <>
                              <Loader2 className="w-4 h-4 animate-spin" />
                              Deleting...
                           </>
                        ) : (
                           <>
                              <Trash2 className="w-4 h-4" />
                              Delete
                           </>
                        )}
                     </Button>
                  </DialogFooter>
               </DialogContent>
            </Dialog>
         )}
      </div>
   );
}
