# Design Document

## Overview

The portfolio data migration system will create a seamless transition from static portfolio data to dynamic database-driven content. The implementation includes a simple admin migration page, data mapping logic, updates to the marketing portfolio page, and enhancements to the ImageGallery component. The system will leverage the existing portfolio management infrastructure while ensuring backward compatibility and proper error handling.

## Architecture

### Migration System Design

The migration system will be implemented as a one-time data transfer utility that reads from the static `data/portfolio.ts` file and populates the MongoDB collections using the existing portfolio service layer.

#### Migration Flow

```mermaid
graph TD
    A[Admin clicks migrate button] --> B[Read data/portfolio.ts]
    B --> C[Map categories to services]
    C --> D[Create portfolio services]
    D --> E[Map images to database format]
    E --> F[Create portfolio images]
    F --> G[Display migration results]
    G --> H[Migration complete]
```

#### Data Mapping Strategy

**Categories to Services Mapping:**

```typescript
// Static category format
{ id: "wedding", name: "Weddings" }

// Maps to PortfolioService
{
  name: "Weddings",
  slug: "wedding", // Use category.id as slug
  isActive: true,
  displayOrder: index, // Based on array position
  description: "", // Empty for migrated data
  createdAt: new Date(),
  updatedAt: new Date()
}
```

**Images to Database Mapping:**

```typescript
// Static image format
{
  src: "/images/wedding-shoots/wedding-shoot-1.JPG",
  alt: "Wedding ceremony moment",
  category: "wedding"
}

// Maps to PortfolioImage
{
  url: "/images/wedding-shoots/wedding-shoot-1.JPG",
  name: "wedding-shoot-1.JPG", // Extracted from src
  altText: "Wedding ceremony moment",
  serviceId: "service_id_for_wedding", // Mapped from category
  width: 0, // Default for migrated data
  height: 0, // Default for migrated data
  fileSize: 0, // Default for migrated data
  mimeType: "image/jpeg", // Inferred from extension
  createdAt: new Date()
}
```

### Marketing Portfolio Page Updates

The marketing portfolio page will be updated to use database content while maintaining the same visual appearance and user experience.

#### Data Flow Changes

**Before (Static):**

```typescript
import { categories, portfolioImages } from "@/data/portfolio";
const filteredImages = portfolioImages[activeCategory] || [];
```

**After (Database):**

```typescript
const { data: services } = useActivePortfolioServices();
const { data: images } = usePortfolioImages();
const filteredImages =
   images?.filter((img) => img.serviceId === activeServiceId) || [];
```

#### Filter Button Enhancement

Filter buttons will display service cover images as backgrounds:

```typescript
interface FilterButtonProps {
   service: PortfolioService;
   isActive: boolean;
   onClick: () => void;
   coverImage?: PortfolioImage;
}
```

The cover image will be the first image associated with each service, determined by creation date or display order.

## Components and Interfaces

### Migration Page Component

**Location:** `app/(admin)/admin/portfolio/migrate/page.tsx`

```typescript
interface MigrationPageProps {}

interface MigrationResult {
   servicesCreated: number;
   imagesCreated: number;
   errors: string[];
   duplicatesSkipped: number;
}
```

**Features:**

-  Simple UI with migration button
-  Progress indicator during migration
-  Results display after completion
-  Error handling and reporting

### Migration Service

**Location:** `lib/services/portfolio-migration-service.ts`

```typescript
interface MigrationService {
   migratePortfolioData(): Promise<MigrationResult>;
   checkExistingData(): Promise<boolean>;
   mapCategoriesToServices(
      categories: Category[]
   ): CreatePortfolioServiceInput[];
   mapImagesToDatabase(
      images: StaticImage[],
      serviceMap: Map<string, string>
   ): CreatePortfolioImageInput[];
}
```

### Updated Marketing Portfolio Page

**Enhanced State Management:**

```typescript
interface PortfolioPageState {
   activeServiceId: string;
   services: PortfolioService[];
   images: PortfolioImage[];
   isLoading: boolean;
   error: string | null;
}
```

**Filter Button Component:**

```typescript
interface FilterButtonProps {
   service: PortfolioService;
   isActive: boolean;
   onClick: () => void;
   coverImage?: PortfolioImage;
}
```

### Enhanced ImageGallery Component

**Updated Props Interface:**

```typescript
interface ImageGalleryProps {
   images: {
      src: string;
      alt: string;
      category?: string;
      width?: number;
      height?: number;
      displayOrder?: number; // New optional prop
   }[];
   showCategory?: boolean;
   className?: string;
   sortByDisplayOrder?: boolean; // New optional prop
}
```

## Data Models

### Migration Data Types

```typescript
// Static data types (from portfolio.ts)
interface StaticCategory {
   id: string;
   name: string;
}

interface StaticImage {
   src: string;
   alt: string;
   category: string;
}

// Migration mapping types
interface ServiceMapping {
   categoryId: string;
   serviceId: string;
   serviceName: string;
}

interface MigrationStats {
   totalCategories: number;
   totalImages: number;
   servicesCreated: number;
   imagesCreated: number;
   duplicatesSkipped: number;
   errors: string[];
}
```

### Enhanced Portfolio Types

```typescript
// Extended PortfolioService for UI
interface PortfolioServiceWithCover extends PortfolioService {
   coverImage?: PortfolioImage;
   imageCount: number;
}

// Enhanced image type for gallery
interface GalleryImage {
   src: string;
   alt: string;
   category?: string;
   width?: number;
   height?: number;
   displayOrder?: number;
}
```

## Implementation Strategy

### Phase 1: Migration Infrastructure

1. **Create Migration Service**

   -  Implement data reading from static file
   -  Create mapping functions
   -  Add duplicate detection logic

2. **Create Migration Page**
   -  Simple admin interface
   -  Migration button and progress display
   -  Results and error reporting

### Phase 2: Marketing Page Updates

1. **Update Data Fetching**

   -  Replace static imports with database queries
   -  Implement proper loading states
   -  Add error handling

2. **Enhance Filter Buttons**
   -  Add cover image backgrounds
   -  Maintain text readability
   -  Implement active state styling

### Phase 3: ImageGallery Enhancement

1. **Add Display Order Support**

   -  Update component props
   -  Implement sorting logic
   -  Maintain backward compatibility

2. **Integration Testing**
   -  Test with migrated data
   -  Verify visual consistency
   -  Validate performance

## Error Handling

### Migration Error Scenarios

1. **File Reading Errors**

   -  Static file not found
   -  Invalid file format
   -  Parse errors

2. **Database Operation Errors**

   -  Connection failures
   -  Validation errors
   -  Constraint violations

3. **Data Mapping Errors**
   -  Invalid image URLs
   -  Missing category mappings
   -  Duplicate data conflicts

### Marketing Page Error Handling

1. **Database Query Failures**

   -  Graceful fallback to empty state
   -  User-friendly error messages
   -  Retry mechanisms

2. **Image Loading Failures**
   -  Placeholder images
   -  Broken image handling
   -  Progressive loading

## Performance Considerations

### Migration Performance

-  **Batch Operations**: Process images in batches to avoid memory issues
-  **Transaction Management**: Use database transactions for data consistency
-  **Progress Reporting**: Provide real-time feedback during migration

### Marketing Page Performance

-  **Query Optimization**: Use efficient database queries with proper indexing
-  **Image Loading**: Implement lazy loading and progressive enhancement
-  **Caching**: Leverage React Query caching for better performance

## Security Considerations

### Migration Security

-  **Admin Authentication**: Ensure only authenticated admins can access migration
-  **Data Validation**: Validate all migrated data before database insertion
-  **Error Logging**: Log migration activities for audit purposes

### Data Integrity

-  **Duplicate Prevention**: Check for existing data before migration
-  **Rollback Capability**: Provide mechanism to undo migration if needed
-  **Backup Recommendations**: Suggest database backup before migration

## Testing Strategy

### Migration Testing

1. **Unit Tests**: Test individual mapping functions
2. **Integration Tests**: Test complete migration flow
3. **Error Scenario Tests**: Test various failure conditions
4. **Performance Tests**: Test with large datasets

### UI Testing

1. **Component Tests**: Test updated components in isolation
2. **Integration Tests**: Test complete page functionality
3. **Visual Regression Tests**: Ensure UI consistency
4. **Accessibility Tests**: Maintain accessibility standards

## Deployment Considerations

### Migration Deployment

-  **Database Backup**: Ensure backup before migration
-  **Rollback Plan**: Prepare rollback procedures
-  **Monitoring**: Monitor migration progress and errors
-  **Documentation**: Provide clear migration instructions

### Feature Deployment

-  **Gradual Rollout**: Consider feature flags for gradual deployment
-  **Performance Monitoring**: Monitor page load times and query performance
-  **Error Tracking**: Implement comprehensive error tracking
-  **User Feedback**: Collect feedback on new functionality

## Future Enhancements

### Potential Improvements

1. **Advanced Migration Options**

   -  Selective migration (specific categories)
   -  Data transformation options
   -  Migration scheduling

2. **Enhanced Portfolio Features**

   -  Image reordering in gallery
   -  Advanced filtering options
   -  Search functionality

3. **Performance Optimizations**

   -  Image optimization
   -  CDN integration
   -  Advanced caching strategies

4. **Analytics Integration**
   -  Portfolio view tracking
   -  Popular category analytics
   -  User engagement metrics
