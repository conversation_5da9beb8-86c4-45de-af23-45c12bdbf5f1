# Design Document

## Overview

The portfolio management system will extend the existing gallery architecture to support dynamic portfolio services and associated images. The system will integrate seamlessly with the current MongoDB-based architecture, R2 storage infrastructure, and admin interface patterns. The implementation follows the established patterns for models, services, actions, and UI components while introducing portfolio-specific functionality.

## Architecture

### Database Design

The system will introduce two new MongoDB collections following the existing patterns:

#### Portfolio Services Collection (`portfolio-services`)

```typescript
interface PortfolioService {
   _id?: ObjectId | string;
   name: string;
   description?: string;
   slug: string; // Autogenerated URL-friendly identifier
   isActive: boolean;
   displayOrder: number;
   createdAt: Date;
   updatedAt: Date;
}
```

#### Portfolio Images Collection (`portfolio-images`)

```typescript
interface PortfolioImage {
   _id?: ObjectId | string;
   url: string;
   name: string;
   altText: string; // Generated with service name pattern
   serviceId: string; // Reference to portfolio service
   width: number;
   height: number;
   fileSize: number;
   mimeType: string;
   createdAt: Date;
}
```

### Integration Points

The system will integrate with existing infrastructure:

-  **R2 Storage**: Reuse existing Cloudflare R2 upload mechanisms
-  **Image Processing**: Leverage existing image validation and metadata extraction
-  **Admin Interface**: Extend current admin sidebar and page structure
-  **Gallery Components**: Adapt existing masonry gallery and upload components

## Components and Interfaces

### Backend Components

#### Models (`lib/models/portfolio.ts`)

-  `PortfolioService` and `PortfolioImage` interfaces
-  Validation functions following existing patterns
-  Metadata creation utilities
-  Type definitions for create/update operations

#### Services (`lib/services/portfolio-service.ts`)

-  CRUD operations for portfolio services
-  CRUD operations for portfolio images
-  Pagination and filtering support
-  Service-image relationship management
-  Search functionality

#### Actions (`lib/actions/portfolio-actions.ts`)

-  Server actions for portfolio operations
-  Image upload handling with service association
-  Bulk operations for image management
-  Integration with existing upload infrastructure

#### Schemas (`lib/schemas/portfolio-schemas.ts`)

-  Zod validation schemas for forms
-  Input validation for API operations

### Frontend Components

#### Admin Interface Extensions

**Admin Sidebar Update**

-  Add "Portfolio" navigation item
-  Integrate with existing navigation patterns

**Portfolio Management Page (`/admin/portfolio`)**

-  Service listing table with CRUD operations
-  Service creation and editing forms
-  Navigation to individual service management

**Service Detail Page (`/admin/portfolio/[serviceId]`)**

-  Service information editing
-  Image gallery for the service
-  Image upload and management tools

#### Specialized Components

**Portfolio Upload Component (`components/portfolio-upload.tsx`)**

-  Based on existing `GalleryUpload` component
-  Service selection dropdown
-  Multi-image upload support
-  Progress tracking and error handling

**Portfolio Gallery Component (`components/portfolio-gallery.tsx`)**

-  Based on existing `MasonryGallery` component
-  Custom action menu (download, delete)
-  Image preview modal
-  Service-specific image management

**Service Selection Dialog**

-  For adding existing images to services
-  Integration with existing gallery components
-  Service picker with search functionality

## Data Models

### Portfolio Service Model

```typescript
// Creation input
interface CreatePortfolioServiceInput {
   name: string;
   description?: string;
   isActive?: boolean;
   displayOrder?: number;
}

// Update input
interface UpdatePortfolioServiceInput {
   name?: string;
   description?: string;
   isActive?: boolean;
   displayOrder?: number;
}

// With stats for display
interface PortfolioServiceWithStats extends PortfolioService {
   imageCount: number;
}
```

### Portfolio Image Model

```typescript
// Creation input
interface CreatePortfolioImageInput {
   url: string;
   name: string;
   serviceId: string;
   width: number;
   height: number;
   fileSize: number;
   mimeType: string;
}

// Update input
interface UpdatePortfolioImageInput {
   name?: string;
   altText?: string;
   serviceId?: string;
}
```

### Alt Text Generation Pattern

Images will have alt text generated following this pattern:

```
"{Service Name} - {Descriptive Text}"
```

Examples:

-  "Wedding Photography - Elegant ceremony at sunset venue"
-  "Pre-Wedding Shoot - Romantic couple portrait session"
-  "Bridal Shower - Intimate celebration with floral arrangements"

## Error Handling

### Database Operations

-  Follow existing error handling patterns from image and album services
-  Proper transaction handling for related operations
-  Graceful degradation for failed operations

### File Operations

-  Leverage existing R2 error handling
-  Rollback database changes if file operations fail
-  Proper cleanup of orphaned files

### UI Error States

-  Form validation with clear error messages
-  Loading states during operations
-  Toast notifications for success/error feedback
-  Retry mechanisms for failed uploads

## Implementation Phases

### Phase 1: Backend Foundation

-  Database models and schemas
-  Service layer implementation
-  Basic CRUD operations

### Phase 2: Admin Interface

-  Admin sidebar extension
-  Portfolio management pages
-  Service CRUD interface
-  Basic image management

### Phase 3: Advanced Features

-  Portfolio-specific upload component
-  Enhanced gallery component
-  Image association from existing galleries
-  Bulk operations

### Phase 4: Integration & Polish

-  Integration with existing gallery components
-  Performance optimization
-  Error handling refinement
-  Documentation and testing

## Security Considerations

### Authentication & Authorization

-  Reuse existing admin authentication
-  Proper session validation
-  Role-based access control

### Data Validation

-  Server-side validation for all inputs
-  File type and size restrictions
-  SQL injection prevention (MongoDB)
-  XSS prevention in user inputs

### File Upload Security

-  Leverage existing R2 security measures
-  File type validation
-  Size limitations
-  Malware scanning considerations

## Performance Considerations

### Database Optimization

-  Proper indexing on frequently queried fields
-  Pagination for large datasets
-  Efficient aggregation queries for stats

### Image Handling

-  Leverage existing image optimization
-  Lazy loading for gallery components
-  Efficient thumbnail generation
-  CDN utilization through R2

### Frontend Performance

-  Component lazy loading
-  Efficient state management
-  Optimized re-renders
-  Bundle size optimization

## Monitoring and Analytics

### Operational Metrics

-  Upload success/failure rates
-  Database query performance
-  Storage usage tracking
-  Error rate monitoring

### Business Metrics

-  Portfolio service usage
-  Image upload patterns
-  Admin user activity
-  System performance metrics

## Migration Strategy

### Data Migration

-  No existing data migration required (new feature)
-  Seed data creation for initial services
-  Backup and recovery procedures

### Deployment Strategy

-  Incremental feature rollout
-  Database schema updates
-  Environment configuration
-  Rollback procedures

## Future Enhancements

### Potential Extensions

-  Public portfolio display pages
-  SEO optimization for portfolio content
-  Advanced image filtering and search
-  Portfolio analytics and insights
-  Client access controls
-  Automated image tagging
-  Integration with social media platforms

### Scalability Considerations

-  Horizontal scaling for high traffic
-  Image processing optimization
-  Database sharding strategies
-  CDN optimization
