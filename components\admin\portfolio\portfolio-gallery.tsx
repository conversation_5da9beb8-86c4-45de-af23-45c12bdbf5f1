"use client";

import PortfolioFloatingActionBar from "@/components/admin/portfolio/portfolio-floating-action-bar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
   Dialog,
   DialogContent,
   DialogDescription,
   DialogHeader,
   DialogTitle,
} from "@/components/ui/dialog";
import {
   DropdownMenu,
   DropdownMenuContent,
   DropdownMenuItem,
   DropdownMenuSeparator,
   DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useReorderPortfolioImages } from "@/lib/hooks/use-portfolio";
import { PortfolioImage } from "@/lib/models/portfolio";
import { cn } from "@/lib/utils";
import {
   DndContext,
   DragEndEvent,
   DragOverlay,
   DragStartEvent,
   KeyboardSensor,
   PointerSensor,
   closestCenter,
   useSensor,
   useSensors,
} from "@dnd-kit/core";
import {
   SortableContext,
   arrayMove,
   rectSortingStrategy,
   sortableKeyboardCoordinates,
   useSortable,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import {
   Download,
   Edit,
   Grip,
   Loader2,
   MoreVertical,
   Move,
   TrashIcon,
} from "lucide-react";
import Image from "next/image";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import PortfolioBulkOperationsDialog from "./portfolio-bulk-operations-dialog";
import PortfolioImageMetadataDialog from "./portfolio-image-metadata-dialog";

interface EnhancedPortfolioGalleryProps {
   images: PortfolioImage[];
   serviceId: string;
   serviceName: string;
   onImageAction?: (imageId: string, action: string) => void;
   showActions?: boolean;
   className?: string;
   // Infinite scroll props
   hasNextPage?: boolean;
   isFetchingNextPage?: boolean;
   fetchNextPage?: () => void;
   isLoading?: boolean;
   // Styles
   columns?: number;
   // Management features
   enableBulkOperations?: boolean;
}

interface ImageModalProps {
   image: PortfolioImage | null;
   isOpen: boolean;
   onClose: () => void;
}

interface SortableImageItemProps {
   image: PortfolioImage;
   isSelected: boolean;
   onSelect: (selected: boolean) => void;
   onImageClick: () => void;
   onAction: (action: string) => void;
   showActions: boolean;
   showCheckboxes: boolean;
   isDragging?: boolean;
}

function SortableImageItem({
   image,
   isSelected,
   onSelect,
   onImageClick,
   onAction,
   showActions,
   showCheckboxes,
   isDragging = false,
}: SortableImageItemProps) {
   const {
      attributes,
      listeners,
      setNodeRef,
      transform,
      transition,
      isDragging: isSortableDragging,
   } = useSortable({ id: image._id!.toString() });

   const style = {
      transform: CSS.Transform.toString(transform),
      transition,
      opacity: isSortableDragging ? 0.5 : 1,
   };

   const handleDownload = async () => {
      try {
         const response = await fetch(image.url);
         if (!response.ok) throw new Error("Failed to fetch image");

         const blob = await response.blob();
         const url = URL.createObjectURL(blob);
         const link = document.createElement("a");
         link.href = url;
         link.download = image.name || `portfolio_image_${image._id}.jpg`;
         document.body.appendChild(link);
         link.click();
         document.body.removeChild(link);
         URL.revokeObjectURL(url);
      } catch (error) {
         console.error("Error downloading image:", error);
         const link = document.createElement("a");
         link.href = image.url;
         link.download = image.name || `portfolio_image_${image._id}.jpg`;
         link.target = "_blank";
         link.rel = "noopener noreferrer";
         document.body.appendChild(link);
         link.click();
         document.body.removeChild(link);
      }
   };

   return (
      <div
         ref={setNodeRef}
         style={style}
         className={cn("group relative cursor-pointer", isDragging && "z-50")}
      >
         <div className="relative overflow-hidden rounded-lg bg-astral-grey/90 h-full">
            <Image
               src={image.url}
               alt={image.altText}
               width={image.width}
               height={image.height}
               className="w-full h-full object-cover transition-transform duration-200 group-hover:scale-105"
               sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, (max-width: 1280px) 25vw, 20vw"
               // onClick={onImageClick}
            />

            {/* Overlay */}
            <div
               className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-200"
               onClick={onImageClick}
            />

            {/* Drag Handle - Top Center */}
            <div
               className="absolute top-2 left-1/2 transform -translate-x-1/2 z-10  opacity-0 group-hover:opacity-100 transition-opacity duration-200 cursor-grab active:cursor-grabbing"
               {...attributes}
               {...listeners}
               onClick={(e) => e.stopPropagation()}
            >
               <div className="bg-background/80 backdrop-blur-sm rounded p-1">
                  <Grip className="size-4" />
               </div>
            </div>

            {/* Checkbox - Top Left */}
            {showCheckboxes && (
               <div
                  className={cn(
                     "absolute top-2 left-2 z-10 transition-opacity duration-200",
                     isSelected
                        ? "opacity-100"
                        : "opacity-100 md:opacity-0 md:group-hover:opacity-100"
                  )}
                  onClick={(e) => e.stopPropagation()}
               >
                  <Checkbox
                     checked={isSelected}
                     onCheckedChange={onSelect}
                     className="bg-background/80 size-5 rounded-sm backdrop-blur-sm border-2"
                  />
               </div>
            )}

            {/* Actions Menu - Top Right */}
            {showActions && (
               <div
                  className="absolute top-2 right-2 sm:opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                  onClick={(e) => e.stopPropagation()}
               >
                  <DropdownMenu>
                     <DropdownMenuTrigger asChild>
                        <Button
                           variant="outline"
                           size="sm"
                           className="h-8 w-8 rounded-lg p-0 bg-background/80 backdrop-blur-sm border-none hover:bg-background/90"
                        >
                           <MoreVertical className="w-4 h-4" />
                           <span className="sr-only">Open menu</span>
                        </Button>
                     </DropdownMenuTrigger>
                     <DropdownMenuContent align="end" className="w-48">
                        <DropdownMenuItem onClick={() => onAction("edit")}>
                           <Edit className="w-4 h-4 mr-2" />
                           Edit Metadata
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={handleDownload}>
                           <Download className="w-4 h-4 mr-2" />
                           Download Image
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => onAction("move")}>
                           <Move className="w-4 h-4 mr-2" />
                           Move to Service
                        </DropdownMenuItem>

                        <DropdownMenuSeparator />

                        <DropdownMenuItem
                           onClick={() => onAction("delete")}
                           className="text-destructive font-semibold hover:!text-white"
                        >
                           <TrashIcon className="w-4 h-4 mr-2" />
                           Delete Image
                        </DropdownMenuItem>
                     </DropdownMenuContent>
                  </DropdownMenu>
               </div>
            )}

            {/* Image info overlay */}
            <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-3 opacity-0 group-hover:opacity-100 transition-opacity">
               <p className="text-white text-sm font-medium truncate">
                  {image.name}
               </p>
               <p className="text-white/80 text-xs">
                  {image.width} × {image.height}
               </p>
            </div>
         </div>
      </div>
   );
}

function ImageModal({ image, isOpen, onClose }: ImageModalProps) {
   const modalDimensions = useMemo(() => {
      if (!image || typeof window === "undefined") {
         return { width: 800, height: 600 };
      }

      const imageAspectRatio = image.width / image.height;
      const maxWidth = Math.min(
         window.innerWidth * 0.95,
         window.innerWidth - 40
      );
      const maxHeight = Math.min(
         window.innerHeight * 0.95,
         window.innerHeight - 40
      );

      let modalWidth = image.width;
      let modalHeight = image.height;

      if (modalWidth > maxWidth) {
         modalWidth = maxWidth;
         modalHeight = modalWidth / imageAspectRatio;
      }

      if (modalHeight > maxHeight) {
         modalHeight = maxHeight;
         modalWidth = modalHeight * imageAspectRatio;
      }

      const minDimension = 450;
      const currentLargerDimension = Math.max(modalWidth, modalHeight);

      if (currentLargerDimension < minDimension) {
         const scaleFactor = minDimension / currentLargerDimension;
         modalWidth *= scaleFactor;
         modalHeight *= scaleFactor;

         if (modalWidth > maxWidth) {
            modalWidth = maxWidth;
            modalHeight = modalWidth / imageAspectRatio;
         }

         if (modalHeight > maxHeight) {
            modalHeight = maxHeight;
            modalWidth = modalHeight * imageAspectRatio;
         }
      }

      return {
         width: Math.round(modalWidth),
         height: Math.round(modalHeight),
      };
   }, [image]);

   if (!image || !modalDimensions) return null;

   return (
      <Dialog open={isOpen} onOpenChange={onClose}>
         <DialogContent
            className="p-0 border-0 bg-transparent shadow-2xl rounded-xl overflow-hidden"
            style={{
               width: `${modalDimensions.width}px`,
               height: `${modalDimensions.height}px`,
               maxWidth: "95vw",
               maxHeight: "95vh",
            }}
         >
            <DialogHeader className="hidden sm:block absolute top-0 left-0 right-0 z-10 p-4">
               <DialogTitle className="text-white text-sm">
                  {image.name}
               </DialogTitle>
               <DialogDescription className="text-gray-100 text-xs font-medium">
                  {image.width} × {image.height} •{" "}
                  {(image.fileSize / 1024 / 1024).toFixed(2)} MB
               </DialogDescription>
            </DialogHeader>
            <div
               className="relative w-full h-full bg-black"
               style={{
                  width: `${modalDimensions.width}px`,
                  height: `${modalDimensions.height}px`,
               }}
            >
               <Image
                  src={image.url}
                  alt={image.altText}
                  width={modalDimensions.width}
                  height={modalDimensions.height}
                  className="object-contain w-full h-full"
                  sizes="95vw"
                  quality={100}
                  priority
               />
            </div>
         </DialogContent>
      </Dialog>
   );
}

export default function EnhancedPortfolioGallery({
   images,
   serviceId,
   serviceName,
   onImageAction,
   showActions = true,
   className,
   hasNextPage = false,
   isFetchingNextPage = false,
   fetchNextPage,
   isLoading = false,
   columns = 3,
   enableBulkOperations = true,
}: EnhancedPortfolioGalleryProps) {
   const [selectedImage, setSelectedImage] = useState<PortfolioImage | null>(
      null
   );
   const [isModalOpen, setIsModalOpen] = useState(false);
   const [selectedImages, setSelectedImages] = useState<Set<string>>(new Set());
   const [draggedImage, setDraggedImage] = useState<PortfolioImage | null>(
      null
   );
   const [localImages, setLocalImages] = useState<PortfolioImage[]>(images);
   const [responsiveColumns, setResponsiveColumns] = useState<number>(columns);

   // Dialog states
   const [isBulkDialogOpen, setIsBulkDialogOpen] = useState(false);
   const [isMetadataDialogOpen, setIsMetadataDialogOpen] = useState(false);
   const [imageToEdit, setImageToEdit] = useState<PortfolioImage | null>(null);

   const observerRef = useRef<IntersectionObserver | null>(null);
   const loadingRef = useRef<HTMLDivElement>(null);

   const reorderMutation = useReorderPortfolioImages();

   // DnD sensors
   const sensors = useSensors(
      useSensor(PointerSensor, {
         activationConstraint: {
            distance: 8,
         },
      }),
      useSensor(KeyboardSensor, {
         coordinateGetter: sortableKeyboardCoordinates,
      })
   );

   // Update local images when props change
   useEffect(() => {
      setLocalImages(images);
   }, [images]);

   // Responsive columns
   useEffect(() => {
      const getResponsiveColumns = () => {
         if (typeof window === "undefined") return columns;
         const width = window.innerWidth;
         if (width < 640) return 1;
         if (width < 768) return Math.min(2, columns);
         if (width < 1024) return Math.min(3, columns);
         return columns;
      };

      const handleResize = () => {
         setResponsiveColumns(getResponsiveColumns());
      };

      handleResize();
      window.addEventListener("resize", handleResize);
      return () => window.removeEventListener("resize", handleResize);
   }, [columns]);

   // Infinite scroll observer
   const handleObserver = useCallback(
      (entries: IntersectionObserverEntry[]) => {
         const [target] = entries;
         if (
            target.isIntersecting &&
            hasNextPage &&
            !isFetchingNextPage &&
            fetchNextPage
         ) {
            fetchNextPage();
         }
      },
      [hasNextPage, isFetchingNextPage, fetchNextPage]
   );

   useEffect(() => {
      const element = loadingRef.current;
      if (!element) return;

      observerRef.current = new IntersectionObserver(handleObserver, {
         rootMargin: "100px",
      });

      observerRef.current.observe(element);

      return () => {
         if (observerRef.current) {
            observerRef.current.disconnect();
         }
      };
   }, [handleObserver]);

   const handleImageClick = (image: PortfolioImage) => {
      setSelectedImage(image);
      setIsModalOpen(true);
   };

   const handleCloseModal = () => {
      setIsModalOpen(false);
      setSelectedImage(null);
   };

   const handleImageSelect = (imageId: string, selected: boolean) => {
      const newSelected = new Set(selectedImages);
      if (selected) {
         newSelected.add(imageId);
      } else {
         newSelected.delete(imageId);
      }
      setSelectedImages(newSelected);
   };

   const handleSelectAll = () => {
      if (selectedImages.size === localImages.length) {
         setSelectedImages(new Set());
      } else {
         setSelectedImages(
            new Set(localImages.map((img) => img._id!.toString()))
         );
      }
   };

   const handleImageAction = (imageId: string, action: string) => {
      const image = localImages.find((img) => img._id?.toString() === imageId);

      switch (action) {
         case "edit":
            if (image) {
               setImageToEdit(image);
               setIsMetadataDialogOpen(true);
            }
            break;
         case "move":
            // Add to selection and open bulk dialog with move operation
            setSelectedImages(new Set([imageId]));
            setIsBulkDialogOpen(true);
            break;
         default:
            onImageAction?.(imageId, action);
      }
   };

   const handleDragStart = (event: DragStartEvent) => {
      const draggedImage = localImages.find(
         (img) => img._id?.toString() === event.active.id
      );
      setDraggedImage(draggedImage || null);
   };

   const handleDragEnd = (event: DragEndEvent) => {
      const { active, over } = event;
      setDraggedImage(null);

      if (!over || active.id === over.id) return;

      const oldIndex = localImages.findIndex(
         (img) => img._id?.toString() === active.id
      );
      const newIndex = localImages.findIndex(
         (img) => img._id?.toString() === over.id
      );

      if (oldIndex === -1 || newIndex === -1) return;

      const newImages = arrayMove(localImages, oldIndex, newIndex);
      setLocalImages(newImages);

      // Create reorder data
      const imageOrder = newImages.map((img, index) => ({
         imageId: img._id!.toString(),
         displayOrder: index,
      }));

      // Submit reorder
      const formData = new FormData();
      formData.append("serviceId", serviceId);
      formData.append("imageOrder", JSON.stringify(imageOrder));

      reorderMutation.mutate(formData);
   };

   const handleBulkSuccess = () => {
      setSelectedImages(new Set());
      setIsBulkDialogOpen(false);
   };

   const handleClearSelection = () => {
      setSelectedImages(new Set());
   };

   const handleBulkActions = () => {
      setIsBulkDialogOpen(true);
   };

   const selectedImageObjects = localImages.filter((img) =>
      selectedImages.has(img._id!.toString())
   );

   if (isLoading) {
      return (
         <div
            className={cn("flex items-center justify-center h-64", className)}
         >
            <div className="flex items-center space-x-2">
               <Loader2 className="h-6 w-6 animate-spin" />
               <span className="text-muted-foreground">
                  Loading portfolio images...
               </span>
            </div>
         </div>
      );
   }

   if (localImages.length === 0) {
      return (
         <div
            className={cn(
               "flex items-center justify-center h-64 text-muted-foreground",
               className
            )}
         >
            <div className="text-center">
               <p>No portfolio images to display</p>
               <p className="text-sm mt-2">Upload some images to get started</p>
            </div>
         </div>
      );
   }

   return (
      <>
         <div className="w-full">
            {/* Gallery Grid */}
            <DndContext
               sensors={sensors}
               collisionDetection={closestCenter}
               onDragStart={handleDragStart}
               onDragEnd={handleDragEnd}
            >
               <SortableContext
                  items={localImages.map((img) => img._id!.toString())}
                  strategy={rectSortingStrategy}
               >
                  <div
                     className="grid gap-4"
                     style={{
                        gridTemplateColumns: `repeat(${responsiveColumns}, 1fr)`,
                     }}
                  >
                     {localImages.map((image) => (
                        <SortableImageItem
                           key={image._id?.toString()}
                           image={image}
                           isSelected={selectedImages.has(
                              image._id!.toString()
                           )}
                           onSelect={(selected) =>
                              handleImageSelect(image._id!.toString(), selected)
                           }
                           onImageClick={() => handleImageClick(image)}
                           onAction={(action) =>
                              handleImageAction(image._id!.toString(), action)
                           }
                           showActions={showActions}
                           showCheckboxes={enableBulkOperations}
                        />
                     ))}
                  </div>
               </SortableContext>

               <DragOverlay>
                  {draggedImage ? (
                     <div className="opacity-90 transform rotate-3 shadow-2xl">
                        <Image
                           src={draggedImage.url}
                           alt={draggedImage.altText}
                           width={200}
                           height={200}
                           className="rounded-lg object-cover"
                        />
                     </div>
                  ) : null}
               </DragOverlay>
            </DndContext>

            {/* Loading indicator for infinite scroll */}
            {(hasNextPage || isFetchingNextPage) && (
               <div
                  ref={loadingRef}
                  className="flex items-center justify-center py-8"
               >
                  <div className="flex items-center space-x-2">
                     <div className="size-5 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin"></div>
                     <span className="text-sm text-muted-foreground">
                        {isFetchingNextPage
                           ? "Loading more images..."
                           : "Scroll to load more"}
                     </span>
                  </div>
               </div>
            )}

            {/* End of content indicator */}
            {!hasNextPage && localImages.length > 0 && (
               <div className="flex items-center justify-center pt-8">
                  <span className="text-sm text-muted-foreground">
                     You&apos;ve reached the end of the portfolio
                  </span>
               </div>
            )}
         </div>

         {/* Image Modal */}
         <ImageModal
            image={selectedImage}
            isOpen={isModalOpen}
            onClose={handleCloseModal}
         />

         {/* Bulk Operations Dialog */}
         {enableBulkOperations && (
            <PortfolioBulkOperationsDialog
               selectedImages={selectedImageObjects}
               currentServiceId={serviceId}
               isOpen={isBulkDialogOpen}
               onClose={() => setIsBulkDialogOpen(false)}
               onSuccess={handleBulkSuccess}
            />
         )}

         {/* Image Metadata Dialog */}
         <PortfolioImageMetadataDialog
            image={imageToEdit}
            serviceName={serviceName}
            isOpen={isMetadataDialogOpen}
            onClose={() => {
               setIsMetadataDialogOpen(false);
               setImageToEdit(null);
            }}
         />

         {/* Floating Action Bar for Bulk Operations */}
         {enableBulkOperations && (
            <PortfolioFloatingActionBar
               selectedCount={selectedImages.size}
               totalCount={localImages.length}
               onClearSelection={handleClearSelection}
               onSelectAll={handleSelectAll}
               onBulkActions={handleBulkActions}
            />
         )}
      </>
   );
}
