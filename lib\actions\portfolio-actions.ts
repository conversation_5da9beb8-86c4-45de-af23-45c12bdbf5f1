"use server";

import { getSession } from "@/lib/auth/session";
import {
   deleteFile as deleteFileFromR2,
   generatePresignedUrl,
} from "@/lib/cloudflare/cloudflare";
import {
   extractFileKeyFromUrl,
   generateUniqueKey,
} from "@/lib/cloudflare/helpers";
import {
   ApiResponse,
   CreatePortfolioImageInput,
   CreatePortfolioServiceInput,
   PortfolioImage,
   PortfolioService,
   PortfolioServiceWithStats,
   UpdatePortfolioImageInput,
   UpdatePortfolioServiceInput,
} from "@/lib/models";
import {
   createPortfolioImageSchema,
   createPortfolioServiceSchema,
   updatePortfolioImageSchema,
   updatePortfolioServiceSchema,
} from "@/lib/schemas/portfolio-schemas";
import { getImageById } from "@/lib/services/image-service";
import {
   forceMigratePortfolioData,
   getMigrationStatus,
   migratePortfolioData,
   MigrationResult,
   SerializableMigrationResult,
   serializeMigrationResult,
} from "@/lib/services/portfolio-migration-service";
import {
   bulkDeletePortfolioImages,
   bulkMovePortfolioImagesToService,
   createPortfolioImage,
   createPortfolioService,
   deletePortfolioImage,
   deletePortfolioService,
   getPortfolioImageById,
   getPortfolioServiceById,
   getPortfolioServices,
   movePortfolioImageToService,
   updatePortfolioImage,
   updatePortfolioService,
} from "@/lib/services/portfolio-service";
import {
   categorizeError,
   createPortfolioError,
   PortfolioErrorType,
} from "@/lib/utils/portfolio-error-handling";
import {
   withDatabaseRetry,
   withUploadRetry,
} from "@/lib/utils/portfolio-retry-operations";
import { SecurityEvents } from "@/lib/utils/security-audit";
import {
   AUTH_ERRORS,
   InputSanitizer,
   PortfolioAuth,
   RateLimiter,
   SecurityValidator,
} from "@/lib/utils/security-utils";

// ===== Portfolio Service Actions =====

/**
 * Create a new portfolio service
 */
export async function createPortfolioServiceAction(
   formData: FormData
): Promise<ApiResponse<PortfolioService>> {
   try {
      // Authentication check
      const session = await getSession();
      if (!session) {
         throw new Error(AUTH_ERRORS.AUTHENTICATION_REQUIRED);
      }

      // Rate limiting check
      const userIdentifier = session.id;
      if (
         RateLimiter.checkActionRateLimit(
            userIdentifier,
            "create_service",
            10,
            60000
         )
      ) {
         return {
            success: false,
            error: "Too many requests. Please wait before creating another service.",
         };
      }

      const name = formData.get("name") as string;
      const description = formData.get("description") as string;
      const coverImageUrl = formData.get("coverImageUrl") as string;
      const isActive = formData.get("isActive") === "true";
      const displayOrder =
         parseInt(formData.get("displayOrder") as string) || 0;

      // Sanitize inputs to prevent XSS
      const sanitizedInputs = InputSanitizer.sanitizePortfolioServiceInput({
         name,
         description: description || undefined,
         coverImageUrl: coverImageUrl || undefined,
      });

      // Validate input using Zod schema
      const validationResult = createPortfolioServiceSchema.safeParse({
         ...sanitizedInputs,
         isActive,
         displayOrder,
      });

      if (!validationResult.success) {
         const errors = validationResult.error.issues.map((err) => err.message);
         const validationError = createPortfolioError(
            PortfolioErrorType.VALIDATION_ERROR,
            `Validation failed: ${errors.join(", ")}`,
            undefined,
            false
         );
         return {
            success: false,
            error: validationError.message,
         };
      }

      const input: CreatePortfolioServiceInput = validationResult.data;

      // Execute with retry for database operations
      const result = await withDatabaseRetry(async () => {
         return await createPortfolioService(input);
      });

      if (!result.success || !result.data) {
         return {
            success: false,
            error:
               result.error?.message || "Failed to create portfolio service",
         };
      }

      // Log successful portfolio service creation
      await SecurityEvents.logPortfolioModification("created", "service", {
         serviceId: result.data._id,
         serviceName: result.data.name,
      });

      return {
         success: true,
         data: result.data,
         message: "Portfolio service created successfully",
      };
   } catch (error) {
      console.error("Error creating portfolio service:", error);
      const portfolioError = categorizeError(error);
      return {
         success: false,
         error: portfolioError.message,
      };
   }
}

/**
 * Update an existing portfolio service
 */
export async function updatePortfolioServiceAction(
   serviceId: string,
   formData: FormData
): Promise<ApiResponse<PortfolioService>> {
   try {
      // Authentication check
      const session = await PortfolioAuth.checkPortfolioPermission();

      // Validate service ID
      if (!SecurityValidator.validateObjectId(serviceId)) {
         return {
            success: false,
            error: "Invalid service ID format",
         };
      }

      // Rate limiting check
      const userIdentifier = session.id;
      if (
         RateLimiter.checkActionRateLimit(
            userIdentifier,
            "update_service",
            20,
            60000
         )
      ) {
         return {
            success: false,
            error: "Too many requests. Please wait before updating again.",
         };
      }

      const name = formData.get("name") as string;
      const description = formData.get("description") as string;
      const coverImageUrl = formData.get("coverImageUrl") as string;
      const isActive = formData.get("isActive");
      const displayOrder = formData.get("displayOrder");

      // Sanitize inputs to prevent XSS
      const sanitizedInputs = InputSanitizer.sanitizePortfolioServiceInput({
         name: name || undefined,
         description: description || undefined,
         coverImageUrl: coverImageUrl || undefined,
      });

      // Build update object with only provided fields
      const updateData: Record<string, unknown> = {};
      if (sanitizedInputs.name) updateData.name = sanitizedInputs.name;
      if (description !== null)
         updateData.description = sanitizedInputs.description || undefined;
      if (coverImageUrl !== null)
         updateData.coverImageUrl = sanitizedInputs.coverImageUrl || undefined;
      if (isActive !== null) updateData.isActive = isActive === "true";
      if (displayOrder !== null)
         updateData.displayOrder = parseInt(displayOrder as string) || 0;

      // Validate input using Zod schema
      const validationResult =
         updatePortfolioServiceSchema.safeParse(updateData);

      if (!validationResult.success) {
         const errors = validationResult.error.issues.map((err) => err.message);
         return {
            success: false,
            error: `Validation failed: ${errors.join(", ")}`,
         };
      }

      const input: UpdatePortfolioServiceInput = validationResult.data;
      const service = await updatePortfolioService(serviceId, input);

      if (!service) {
         return {
            success: false,
            error: "Portfolio service not found",
         };
      }

      return {
         success: true,
         data: service,
         message: "Portfolio service updated successfully",
      };
   } catch (error) {
      console.error("Error updating portfolio service:", error);
      return {
         success: false,
         error:
            error instanceof Error
               ? error.message
               : "Failed to update portfolio service",
      };
   }
}

/**
 * Delete a portfolio service
 */
export async function deletePortfolioServiceAction(
   serviceId: string
): Promise<ApiResponse> {
   try {
      // Authentication check
      const session = await PortfolioAuth.checkPortfolioPermission();

      // Validate service ID
      if (!SecurityValidator.validateObjectId(serviceId)) {
         return {
            success: false,
            error: "Invalid service ID format",
         };
      }

      // Rate limiting check
      const userIdentifier = session.id;
      if (
         RateLimiter.checkActionRateLimit(
            userIdentifier,
            "delete_service",
            5,
            60000
         )
      ) {
         return {
            success: false,
            error: "Too many delete requests. Please wait before trying again.",
         };
      }

      const deleted = await deletePortfolioService(serviceId);

      if (!deleted) {
         return {
            success: false,
            error: "Portfolio service not found",
         };
      }

      return {
         success: true,
         message: "Portfolio service deleted successfully",
      };
   } catch (error) {
      console.error("Error deleting portfolio service:", error);
      return {
         success: false,
         error:
            error instanceof Error
               ? error.message
               : "Failed to delete portfolio service",
      };
   }
}

/**
 * Get all portfolio services
 */
export async function getPortfolioServicesAction(): Promise<
   ApiResponse<PortfolioServiceWithStats[]>
> {
   try {
      // Authentication check
      await PortfolioAuth.checkPortfolioPermission();

      const result = await getPortfolioServices();

      return {
         success: true,
         data: result.data,
         message: "Portfolio services retrieved successfully",
      };
   } catch (error) {
      console.error("Error fetching portfolio services:", error);
      return {
         success: false,
         error:
            error instanceof Error
               ? error.message
               : "Failed to fetch portfolio services",
      };
   }
}

/**
 * Get portfolio service by ID
 */
export async function getPortfolioServiceByIdAction(
   serviceId: string
): Promise<ApiResponse<PortfolioServiceWithStats>> {
   try {
      // Authentication check
      await PortfolioAuth.checkPortfolioPermission();

      // Validate service ID
      if (!SecurityValidator.validateObjectId(serviceId)) {
         return {
            success: false,
            error: "Invalid service ID format",
         };
      }

      const service = await getPortfolioServiceById(serviceId);

      if (!service) {
         return {
            success: false,
            error: "Portfolio service not found",
         };
      }

      return {
         success: true,
         data: service,
         message: "Portfolio service retrieved successfully",
      };
   } catch (error) {
      console.error("Error fetching portfolio service:", error);
      return {
         success: false,
         error:
            error instanceof Error
               ? error.message
               : "Failed to fetch portfolio service",
      };
   }
}

// ===== Portfolio Image Actions =====

/**
 * Generate pre-signed URL for direct portfolio image upload
 */
export async function generatePortfolioUploadUrl(
   filename: string,
   contentType: string,
   fileSize: number
): Promise<ApiResponse> {
   try {
      // Authentication check
      const session = await PortfolioAuth.checkPortfolioPermission();

      // Rate limiting check for uploads
      const userIdentifier = session.id;
      if (RateLimiter.checkUploadRateLimit(userIdentifier, 20, 60000)) {
         await SecurityEvents.logRateLimitExceeded(
            "portfolio_upload",
            userIdentifier
         );
         return {
            success: false,
            error: "Upload rate limit exceeded. Please wait before uploading more files.",
         };
      }

      // Sanitize filename
      const sanitizedFilename = InputSanitizer.sanitizeFilename(filename);
      if (!sanitizedFilename) {
         return {
            success: false,
            error: "Invalid filename provided",
         };
      }

      // Security validation for file upload
      const fileValidation = SecurityValidator.validateFileUpload(
         sanitizedFilename,
         contentType,
         fileSize
      );

      if (!fileValidation.isValid) {
         await SecurityEvents.logFileUploadBlocked(
            sanitizedFilename,
            fileValidation.errors.join(", ")
         );
         return {
            success: false,
            error: fileValidation.errors.join(", "),
         };
      }

      // Generate unique key with sanitized filename
      const key = generateUniqueKey(sanitizedFilename);

      // Generate pre-signed URL with retry for network operations
      const result = await withUploadRetry(async () => {
         return await generatePresignedUrl(key, contentType);
      });

      if (!result.success || !result.data) {
         return {
            success: false,
            error: result.error?.message || "Failed to generate upload URL",
         };
      }

      return {
         success: true,
         data: {
            uploadUrl: result.data,
            key: key,
            publicUrl: `${process.env.R2_APP_DOMAIN}/${key}`,
         },
         message: "Upload URL generated successfully",
      };
   } catch (error) {
      console.error("Error generating portfolio upload URL:", error);
      const portfolioError = categorizeError(error);
      return {
         success: false,
         error: portfolioError.message,
      };
   }
}

/**
 * Save portfolio image metadata after successful direct upload
 */
export async function savePortfolioImageMetadata(
   key: string,
   filename: string,
   contentType: string,
   fileSize: number,
   width: number,
   height: number,
   serviceId: string
): Promise<ApiResponse<PortfolioImage>> {
   try {
      // Authentication check
      await PortfolioAuth.checkPortfolioPermission();

      // Validate service ID
      if (!SecurityValidator.validateObjectId(serviceId)) {
         return {
            success: false,
            error: "Invalid service ID format",
         };
      }

      // Sanitize inputs
      const sanitizedFilename = InputSanitizer.sanitizeFilename(filename);
      const sanitizedUrl = InputSanitizer.sanitizeUrl(
         `${process.env.R2_APP_DOMAIN}/${key}`
      );

      if (!sanitizedFilename || !sanitizedUrl) {
         return {
            success: false,
            error: "Invalid filename or URL provided",
         };
      }

      // Validate input using Zod schema
      const imageInput = {
         url: sanitizedUrl,
         name: sanitizedFilename,
         serviceId,
         width,
         height,
         fileSize,
         mimeType: contentType,
      };

      const validationResult = createPortfolioImageSchema.safeParse(imageInput);

      if (!validationResult.success) {
         const errors = validationResult.error.issues.map((err) => err.message);
         return {
            success: false,
            error: `Validation failed: ${errors.join(", ")}`,
         };
      }

      const input: CreatePortfolioImageInput = validationResult.data;
      const savedImage = await createPortfolioImage(input);

      return {
         success: true,
         data: savedImage,
         message: "Portfolio image metadata saved successfully",
      };
   } catch (error) {
      console.error("Error saving portfolio image metadata:", error);
      return {
         success: false,
         error:
            error instanceof Error
               ? error.message
               : "Failed to save portfolio image metadata",
      };
   }
}

/**
 * Update portfolio image
 */
export async function updatePortfolioImageAction(
   imageId: string,
   formData: FormData
): Promise<ApiResponse<PortfolioImage>> {
   try {
      // Authentication check
      const session = await PortfolioAuth.checkPortfolioPermission();

      // Validate image ID
      if (!SecurityValidator.validateObjectId(imageId)) {
         return {
            success: false,
            error: "Invalid image ID format",
         };
      }

      // Rate limiting check
      const userIdentifier = session.id;
      if (
         RateLimiter.checkActionRateLimit(
            userIdentifier,
            "update_image",
            30,
            60000
         )
      ) {
         return {
            success: false,
            error: "Too many update requests. Please wait before trying again.",
         };
      }

      const name = formData.get("name") as string;
      const altText = formData.get("altText") as string;
      const serviceId = formData.get("serviceId") as string;

      // Sanitize inputs
      const sanitizedInputs = InputSanitizer.sanitizePortfolioImageInput({
         name: name || undefined,
         altText: altText || undefined,
      });

      // Validate service ID if provided
      if (serviceId && !SecurityValidator.validateObjectId(serviceId)) {
         return {
            success: false,
            error: "Invalid service ID format",
         };
      }

      // Build update object with only provided fields
      const updateData: Record<string, unknown> = {};
      if (sanitizedInputs.name) updateData.name = sanitizedInputs.name;
      if (sanitizedInputs.altText) updateData.altText = sanitizedInputs.altText;
      if (serviceId) updateData.serviceId = serviceId;

      // Validate input using Zod schema
      const validationResult = updatePortfolioImageSchema.safeParse(updateData);

      if (!validationResult.success) {
         const errors = validationResult.error.issues.map((err) => err.message);
         return {
            success: false,
            error: `Validation failed: ${errors.join(", ")}`,
         };
      }

      const input: UpdatePortfolioImageInput = validationResult.data;
      const image = await updatePortfolioImage(imageId, input);

      if (!image) {
         return {
            success: false,
            error: "Portfolio image not found",
         };
      }

      return {
         success: true,
         data: image,
         message: "Portfolio image updated successfully",
      };
   } catch (error) {
      console.error("Error updating portfolio image:", error);
      return {
         success: false,
         error:
            error instanceof Error
               ? error.message
               : "Failed to update portfolio image",
      };
   }
}

/**
 * Delete portfolio image from R2 and database
 */
export async function deletePortfolioImageAction(
   imageId: string
): Promise<ApiResponse> {
   try {
      // Authentication check
      const session = await PortfolioAuth.checkPortfolioPermission();

      // Validate image ID
      if (!SecurityValidator.validateObjectId(imageId)) {
         return {
            success: false,
            error: "Invalid image ID format",
         };
      }

      // Rate limiting check
      const userIdentifier = session.id;
      if (
         RateLimiter.checkActionRateLimit(
            userIdentifier,
            "delete_image",
            10,
            60000
         )
      ) {
         return {
            success: false,
            error: "Too many delete requests. Please wait before trying again.",
         };
      }

      // First get the image to get the R2 key
      const image = await getPortfolioImageById(imageId);

      if (!image) {
         return {
            success: false,
            error: "Portfolio image not found",
         };
      }

      // Check if the URL is a full URL (starts with http/https) or a relative path
      // Only attempt R2 deletion for full URLs, skip for relative paths
      if (image.url.startsWith("http://") || image.url.startsWith("https://")) {
         try {
            // Extract key from URL using helper function
            const key = extractFileKeyFromUrl(image.url);

            // Delete from R2
            await deleteFileFromR2(key);
         } catch (error) {
            console.warn(
               `Failed to delete from R2 storage (URL: ${image.url}):`,
               error
            );
            // Continue with database deletion even if R2 deletion fails
         }
      } else {
         console.log(`Skipping R2 deletion for relative URL: ${image.url}`);
      }

      // Delete from database
      const deleted = await deletePortfolioImage(imageId);

      if (!deleted) {
         return {
            success: false,
            error: "Failed to delete portfolio image from database",
         };
      }

      return {
         success: true,
         message: "Portfolio image deleted successfully",
      };
   } catch (error) {
      console.error("Error deleting portfolio image:", error);
      return {
         success: false,
         error:
            error instanceof Error
               ? error.message
               : "Failed to delete portfolio image",
      };
   }
}

/**
 * Add existing images to a portfolio service
 */
export async function addExistingImagesToPortfolioService(
   formData: FormData
): Promise<ApiResponse> {
   try {
      // Authentication check
      const session = await PortfolioAuth.checkPortfolioPermission();

      const serviceId = formData.get("serviceId") as string;
      const imageIdsString = formData.get("imageIds") as string;

      // Validate service ID
      if (!serviceId || !SecurityValidator.validateObjectId(serviceId)) {
         return {
            success: false,
            error: "Valid service ID is required",
         };
      }

      if (!imageIdsString) {
         return {
            success: false,
            error: "Image IDs are required",
         };
      }

      let imageIds: string[];
      try {
         imageIds = JSON.parse(imageIdsString);
      } catch {
         return {
            success: false,
            error: "Invalid image IDs format",
         };
      }

      // Validate image IDs
      const idsValidation = SecurityValidator.validateObjectIds(imageIds);
      if (!idsValidation.isValid) {
         return {
            success: false,
            error: idsValidation.errors.join(", "),
         };
      }

      // Rate limiting check
      const userIdentifier = session.id;
      if (
         RateLimiter.checkActionRateLimit(
            userIdentifier,
            "add_images",
            15,
            60000
         )
      ) {
         return {
            success: false,
            error: "Too many requests. Please wait before adding more images.",
         };
      }

      // Verify service exists
      const service = await getPortfolioServiceById(serviceId);
      if (!service) {
         return {
            success: false,
            error: "Portfolio service not found",
         };
      }

      // Verify all images exist and create portfolio images
      const results = [];
      const errors = [];

      for (const imageId of imageIds) {
         try {
            const existingImage = await getImageById(imageId);
            if (!existingImage) {
               errors.push(`Image ${imageId} not found`);
               continue;
            }

            // Create portfolio image from existing image
            const portfolioImageInput: CreatePortfolioImageInput = {
               url: existingImage.url,
               name: existingImage.name,
               serviceId: serviceId,
               width: existingImage.width,
               height: existingImage.height,
               fileSize: existingImage.fileSize,
               mimeType: existingImage.mimeType,
            };

            const portfolioImage = await createPortfolioImage(
               portfolioImageInput
            );
            results.push(portfolioImage);
         } catch (error) {
            const errorMessage =
               error instanceof Error ? error.message : "Unknown error";
            errors.push(`Failed to add image ${imageId}: ${errorMessage}`);
         }
      }

      if (errors.length > 0 && results.length === 0) {
         return {
            success: false,
            error: `All operations failed: ${errors.join(", ")}`,
         };
      }

      return {
         success: true,
         data: {
            added: results,
            errors: errors.length > 0 ? errors : undefined,
         },
         message: `Successfully added ${results.length} of ${imageIds.length} images to portfolio service`,
      };
   } catch (error) {
      console.error(
         "Error adding existing images to portfolio service:",
         error
      );
      return {
         success: false,
         error:
            error instanceof Error
               ? error.message
               : "Failed to add images to portfolio service",
      };
   }
}

/**
 * Move portfolio image to different service
 */
export async function movePortfolioImageToServiceAction(
   imageId: string,
   serviceId: string
): Promise<ApiResponse<PortfolioImage>> {
   try {
      // Authentication check
      const session = await PortfolioAuth.checkPortfolioPermission();

      // Validate IDs
      if (!imageId || !SecurityValidator.validateObjectId(imageId)) {
         return {
            success: false,
            error: "Valid image ID is required",
         };
      }

      if (!serviceId || !SecurityValidator.validateObjectId(serviceId)) {
         return {
            success: false,
            error: "Valid service ID is required",
         };
      }

      // Rate limiting check
      const userIdentifier = session.id;
      if (
         RateLimiter.checkActionRateLimit(
            userIdentifier,
            "move_image",
            20,
            60000
         )
      ) {
         return {
            success: false,
            error: "Too many move requests. Please wait before trying again.",
         };
      }

      // Verify service exists
      const service = await getPortfolioServiceById(serviceId);
      if (!service) {
         return {
            success: false,
            error: "Portfolio service not found",
         };
      }

      const image = await movePortfolioImageToService(imageId, serviceId);

      if (!image) {
         return {
            success: false,
            error: "Portfolio image not found",
         };
      }

      return {
         success: true,
         data: image,
         message: "Portfolio image moved successfully",
      };
   } catch (error) {
      console.error("Error moving portfolio image to service:", error);
      return {
         success: false,
         error:
            error instanceof Error
               ? error.message
               : "Failed to move portfolio image",
      };
   }
}

/**
 * Bulk move portfolio images to service
 */
export async function bulkMovePortfolioImagesToServiceAction(
   formData: FormData
): Promise<ApiResponse> {
   try {
      // Authentication check
      const session = await PortfolioAuth.checkPortfolioPermission();

      const serviceId = formData.get("serviceId") as string;
      const imageIdsString = formData.get("imageIds") as string;

      // Validate service ID
      if (!serviceId || !SecurityValidator.validateObjectId(serviceId)) {
         return {
            success: false,
            error: "Valid service ID is required",
         };
      }

      if (!imageIdsString) {
         return {
            success: false,
            error: "Image IDs are required",
         };
      }

      let imageIds: string[];
      try {
         imageIds = JSON.parse(imageIdsString);
      } catch {
         return {
            success: false,
            error: "Invalid image IDs format",
         };
      }

      // Validate image IDs
      const idsValidation = SecurityValidator.validateObjectIds(imageIds);
      if (!idsValidation.isValid) {
         return {
            success: false,
            error: idsValidation.errors.join(", "),
         };
      }

      // Rate limiting check
      const userIdentifier = session.id;
      if (
         RateLimiter.checkActionRateLimit(userIdentifier, "bulk_move", 5, 60000)
      ) {
         return {
            success: false,
            error: "Too many bulk operations. Please wait before trying again.",
         };
      }

      // Verify service exists
      const service = await getPortfolioServiceById(serviceId);
      if (!service) {
         return {
            success: false,
            error: "Portfolio service not found",
         };
      }

      const success = await bulkMovePortfolioImagesToService(
         imageIds,
         serviceId
      );

      if (!success) {
         return {
            success: false,
            error: "Failed to move portfolio images",
         };
      }

      return {
         success: true,
         message: `Successfully moved ${imageIds.length} images to portfolio service`,
      };
   } catch (error) {
      console.error("Error bulk moving portfolio images:", error);
      return {
         success: false,
         error:
            error instanceof Error
               ? error.message
               : "Failed to move portfolio images",
      };
   }
}

/**
 * Bulk delete portfolio images
 */
export async function bulkDeletePortfolioImagesAction(
   formData: FormData
): Promise<ApiResponse> {
   try {
      // Authentication check
      const session = await PortfolioAuth.checkPortfolioPermission();

      const imageIdsString = formData.get("imageIds") as string;

      if (!imageIdsString) {
         return {
            success: false,
            error: "Image IDs are required",
         };
      }

      let imageIds: string[];
      try {
         imageIds = JSON.parse(imageIdsString);
      } catch {
         return {
            success: false,
            error: "Invalid image IDs format",
         };
      }

      // Validate image IDs
      const idsValidation = SecurityValidator.validateObjectIds(imageIds);
      if (!idsValidation.isValid) {
         return {
            success: false,
            error: idsValidation.errors.join(", "),
         };
      }

      // Rate limiting check
      const userIdentifier = session.id;
      if (
         RateLimiter.checkActionRateLimit(
            userIdentifier,
            "bulk_delete",
            3,
            60000
         )
      ) {
         return {
            success: false,
            error: "Too many bulk delete operations. Please wait before trying again.",
         };
      }

      // Get all images to delete from R2 storage
      const imagesToDelete = [];
      for (const imageId of imageIds) {
         const image = await getPortfolioImageById(imageId);
         if (image) {
            imagesToDelete.push(image);
         }
      }

      // Delete from R2 storage (only for full URLs)
      for (const image of imagesToDelete) {
         // Check if the URL is a full URL (starts with http/https) or a relative path
         if (
            image.url.startsWith("http://") ||
            image.url.startsWith("https://")
         ) {
            try {
               const key = extractFileKeyFromUrl(image.url);
               await deleteFileFromR2(key);
            } catch (error) {
               console.error(
                  `Failed to delete image from R2: ${image.url}`,
                  error
               );
               // Continue with database deletion even if R2 deletion fails
            }
         } else {
            console.log(`Skipping R2 deletion for relative URL: ${image.url}`);
         }
      }

      // Delete from database
      const success = await bulkDeletePortfolioImages(imageIds);

      if (!success) {
         return {
            success: false,
            error: "Failed to delete portfolio images from database",
         };
      }

      return {
         success: true,
         message: `Successfully deleted ${imageIds.length} portfolio images`,
      };
   } catch (error) {
      console.error("Error bulk deleting portfolio images:", error);
      return {
         success: false,
         error:
            error instanceof Error
               ? error.message
               : "Failed to delete portfolio images",
      };
   }
}

/**
 * Reorder portfolio images within a service
 */
export async function reorderPortfolioImagesAction(
   formData: FormData
): Promise<ApiResponse> {
   try {
      // Authentication check
      const session = await PortfolioAuth.checkPortfolioPermission();

      const serviceId = formData.get("serviceId") as string;
      const imageOrderString = formData.get("imageOrder") as string;

      // Validate service ID
      if (!serviceId || !SecurityValidator.validateObjectId(serviceId)) {
         return {
            success: false,
            error: "Valid service ID is required",
         };
      }

      if (!imageOrderString) {
         return {
            success: false,
            error: "Image order is required",
         };
      }

      let imageOrder: Array<{ imageId: string; displayOrder: number }>;
      try {
         imageOrder = JSON.parse(imageOrderString);
      } catch {
         return {
            success: false,
            error: "Invalid image order format",
         };
      }

      if (!Array.isArray(imageOrder) || imageOrder.length === 0) {
         return {
            success: false,
            error: "At least one image order must be specified",
         };
      }

      // Validate all image IDs in the order array
      const imageIds = imageOrder.map((item) => item.imageId);
      const idsValidation = SecurityValidator.validateObjectIds(imageIds);
      if (!idsValidation.isValid) {
         return {
            success: false,
            error: idsValidation.errors.join(", "),
         };
      }

      // Rate limiting check
      const userIdentifier = session.id;
      if (
         RateLimiter.checkActionRateLimit(
            userIdentifier,
            "reorder_images",
            10,
            60000
         )
      ) {
         return {
            success: false,
            error: "Too many reorder requests. Please wait before trying again.",
         };
      }

      // Validate service exists
      const service = await getPortfolioServiceById(serviceId);
      if (!service) {
         return {
            success: false,
            error: "Portfolio service not found",
         };
      }

      const { reorderPortfolioImages } = await import(
         "@/lib/services/portfolio-service"
      );
      const success = await reorderPortfolioImages(serviceId, imageOrder);

      if (!success) {
         return {
            success: false,
            error: "Failed to reorder portfolio images",
         };
      }

      return {
         success: true,
         message: `Successfully reordered ${imageOrder.length} portfolio images`,
      };
   } catch (error) {
      console.error("Error reordering portfolio images:", error);
      return {
         success: false,
         error:
            error instanceof Error
               ? error.message
               : "Failed to reorder portfolio images",
      };
   }
}

/**
 * Reorder portfolio services
 */
export async function reorderPortfolioServicesAction(
   formData: FormData
): Promise<ApiResponse> {
   try {
      // Authentication check
      const session = await PortfolioAuth.checkPortfolioPermission();

      const serviceOrderString = formData.get("serviceOrder") as string;

      if (!serviceOrderString) {
         return {
            success: false,
            error: "Service order is required",
         };
      }

      let serviceOrder: Array<{ serviceId: string; displayOrder: number }>;
      try {
         serviceOrder = JSON.parse(serviceOrderString);
      } catch {
         return {
            success: false,
            error: "Invalid service order format",
         };
      }

      if (!Array.isArray(serviceOrder) || serviceOrder.length === 0) {
         return {
            success: false,
            error: "At least one service order must be specified",
         };
      }

      // Validate all service IDs in the order array
      const serviceIds = serviceOrder.map((item) => item.serviceId);
      const idsValidation = SecurityValidator.validateObjectIds(serviceIds);
      if (!idsValidation.isValid) {
         return {
            success: false,
            error: idsValidation.errors.join(", "),
         };
      }

      // Rate limiting check
      const userIdentifier = session.id;
      if (
         RateLimiter.checkActionRateLimit(
            userIdentifier,
            "reorder_services",
            10,
            60000
         )
      ) {
         return {
            success: false,
            error: "Too many reorder requests. Please wait before trying again.",
         };
      }

      const { reorderPortfolioServices } = await import(
         "@/lib/services/portfolio-service"
      );
      const success = await reorderPortfolioServices(serviceOrder);

      if (!success) {
         return {
            success: false,
            error: "Failed to reorder portfolio services",
         };
      }

      return {
         success: true,
         message: `Successfully reordered ${serviceOrder.length} portfolio services`,
      };
   } catch (error) {
      console.error("Error reordering portfolio services:", error);
      return {
         success: false,
         error:
            error instanceof Error
               ? error.message
               : "Failed to reorder portfolio services",
      };
   }
}
// ===== Portfolio Migration Actions =====

/**
 * Migrate portfolio data from static files to database
 */
export async function migratePortfolioDataAction(): Promise<
   ApiResponse<SerializableMigrationResult>
> {
   try {
      // Authentication check
      const session = await PortfolioAuth.checkPortfolioPermission();

      // Rate limiting check for migration operations
      const userIdentifier = session.id;
      if (
         RateLimiter.checkActionRateLimit(
            userIdentifier,
            "migrate_portfolio",
            1,
            300000 // 5 minutes - migration should be rare
         )
      ) {
         await SecurityEvents.logRateLimitExceeded(
            "portfolio_migration",
            userIdentifier
         );
         return {
            success: false,
            error: "Migration rate limit exceeded. Please wait before attempting migration again.",
         };
      }

      // Log migration attempt for security audit
      await SecurityEvents.logPortfolioModification(
         "migration_started",
         "portfolio",
         {
            userId: session.id,
            timestamp: new Date().toISOString(),
         }
      );

      // Execute migration with database retry for resilience
      const retryResult = await withDatabaseRetry(async () => {
         return await migratePortfolioData();
      });

      // Check if the retry operation itself failed
      if (!retryResult.success || !retryResult.data) {
         await SecurityEvents.logPortfolioModification(
            "migration_failed",
            "portfolio",
            {
               userId: session.id,
               error:
                  retryResult.error?.message || "Migration operation failed",
               attempts: retryResult.attempts,
            }
         );

         return {
            success: false,
            error: retryResult.error?.message || "Migration operation failed",
         };
      }

      const migrationResult = retryResult.data;

      // Check if migration had errors
      if (migrationResult.errors.length > 0) {
         // Log migration errors for audit
         await SecurityEvents.logPortfolioModification(
            "migration_failed",
            "portfolio",
            {
               userId: session.id,
               errors: migrationResult.errors,
               partialSuccess:
                  migrationResult.servicesCreated > 0 ||
                  migrationResult.imagesCreated > 0,
            }
         );

         // If there were some successes, return partial success
         if (
            migrationResult.servicesCreated > 0 ||
            migrationResult.imagesCreated > 0
         ) {
            return {
               success: true,
               data: migrationResult,
               message: `Migration completed with warnings. Created ${
                  migrationResult.servicesCreated
               } services and ${
                  migrationResult.imagesCreated
               } images. Errors: ${migrationResult.errors.join(", ")}`,
            };
         }

         // Complete failure
         return {
            success: false,
            error: `Migration failed: ${migrationResult.errors.join(", ")}`,
            data: migrationResult,
         };
      }

      // Log successful migration
      await SecurityEvents.logPortfolioModification(
         "migration_completed",
         "portfolio",
         {
            userId: session.id,
            servicesCreated: migrationResult.servicesCreated,
            imagesCreated: migrationResult.imagesCreated,
            duplicatesSkipped: migrationResult.duplicatesSkipped,
         }
      );

      return {
         success: true,
         data: migrationResult,
         message: `Migration completed successfully. Created ${migrationResult.servicesCreated} services and ${migrationResult.imagesCreated} images.`,
      };
   } catch (error) {
      console.error("Error during portfolio migration:", error);

      // Log migration error for security audit
      try {
         const session = await getSession();
         if (session) {
            await SecurityEvents.logPortfolioModification(
               "migration_error",
               "portfolio",
               {
                  userId: session.id,
                  error:
                     error instanceof Error ? error.message : "Unknown error",
               }
            );
         }
      } catch (logError) {
         console.error("Failed to log migration error:", logError);
      }

      const portfolioError = categorizeError(error);
      return {
         success: false,
         error: portfolioError.message,
      };
   }
}

/**
 * Force migrate portfolio data with update strategy
 */
export async function forceMigratePortfolioDataAction(): Promise<
   ApiResponse<MigrationResult>
> {
   try {
      // Authentication check
      const session = await PortfolioAuth.checkPortfolioPermission();

      // Rate limiting check for force migration operations
      const userIdentifier = session.id;
      if (
         RateLimiter.checkActionRateLimit(
            userIdentifier,
            "force_migrate_portfolio",
            1,
            600000 // 10 minutes - force migration should be very rare
         )
      ) {
         await SecurityEvents.logRateLimitExceeded(
            "portfolio_force_migration",
            userIdentifier
         );
         return {
            success: false,
            error: "Force migration rate limit exceeded. Please wait before attempting force migration again.",
         };
      }

      // Log force migration attempt for security audit
      await SecurityEvents.logPortfolioModification(
         "force_migration_started",
         "portfolio",
         {
            userId: session.id,
            timestamp: new Date().toISOString(),
         }
      );

      // Execute force migration with database retry for resilience
      const retryResult = await withDatabaseRetry(async () => {
         return await forceMigratePortfolioData();
      });

      // Check if the retry operation itself failed
      if (!retryResult.success || !retryResult.data) {
         await SecurityEvents.logPortfolioModification(
            "force_migration_failed",
            "portfolio",
            {
               userId: session.id,
               error:
                  retryResult.error?.message ||
                  "Force migration operation failed",
               attempts: retryResult.attempts,
            }
         );

         return {
            success: false,
            error:
               retryResult.error?.message || "Force migration operation failed",
         };
      }

      const migrationResult = retryResult.data;

      // Check if migration had errors
      if (migrationResult.errors.length > 0) {
         // Log migration errors for audit
         await SecurityEvents.logPortfolioModification(
            "force_migration_failed",
            "portfolio",
            {
               userId: session.id,
               errors: migrationResult.errors,
               partialSuccess:
                  migrationResult.servicesCreated > 0 ||
                  migrationResult.imagesCreated > 0 ||
                  migrationResult.servicesUpdated > 0 ||
                  migrationResult.imagesUpdated > 0,
            }
         );

         // If there were some successes, return partial success
         const totalProcessed =
            migrationResult.servicesCreated +
            migrationResult.servicesUpdated +
            migrationResult.imagesCreated +
            migrationResult.imagesUpdated;

         if (totalProcessed > 0) {
            return {
               success: true,
               data: migrationResult,
               message: `Force migration completed with warnings. Created ${
                  migrationResult.servicesCreated
               } services, updated ${
                  migrationResult.servicesUpdated
               } services, created ${
                  migrationResult.imagesCreated
               } images, updated ${
                  migrationResult.imagesUpdated
               } images. Errors: ${migrationResult.errors.join(", ")}`,
            };
         }

         // Complete failure
         return {
            success: false,
            error: `Force migration failed: ${migrationResult.errors.join(
               ", "
            )}`,
            data: migrationResult,
         };
      }

      // Log successful force migration
      await SecurityEvents.logPortfolioModification(
         "force_migration_completed",
         "portfolio",
         {
            userId: session.id,
            servicesCreated: migrationResult.servicesCreated,
            servicesUpdated: migrationResult.servicesUpdated,
            imagesCreated: migrationResult.imagesCreated,
            imagesUpdated: migrationResult.imagesUpdated,
            duplicatesSkipped: migrationResult.duplicatesSkipped,
         }
      );

      return {
         success: true,
         data: migrationResult,
         message: `Force migration completed successfully. Created ${migrationResult.servicesCreated} services, updated ${migrationResult.servicesUpdated} services, created ${migrationResult.imagesCreated} images, updated ${migrationResult.imagesUpdated} images.`,
      };
   } catch (error) {
      console.error("Error during portfolio force migration:", error);

      // Log migration error for security audit
      try {
         const session = await getSession();
         if (session) {
            await SecurityEvents.logPortfolioModification(
               "force_migration_error",
               "portfolio",
               {
                  userId: session.id,
                  error:
                     error instanceof Error ? error.message : "Unknown error",
               }
            );
         }
      } catch (logError) {
         console.error("Failed to log force migration error:", logError);
      }

      const portfolioError = categorizeError(error);
      return {
         success: false,
         error: portfolioError.message,
      };
   }
}

/**
 * Get migration status including existing data and duplicate detection
 */
export async function getMigrationStatusAction(): Promise<
   ApiResponse<{
      hasExistingData: boolean;
      existingServicesCount: number;
      existingImagesCount: number;
      duplicateDetection: {
         duplicateServiceSlugs: string[];
         duplicateImageUrls: string[];
      } | null;
   }>
> {
   try {
      // Authentication check
      const session = await PortfolioAuth.checkPortfolioPermission();

      // Rate limiting check for status checks
      const userIdentifier = session.id;
      if (
         RateLimiter.checkActionRateLimit(
            userIdentifier,
            "migration_status",
            10,
            60000 // 1 minute
         )
      ) {
         return {
            success: false,
            error: "Status check rate limit exceeded. Please wait before checking again.",
         };
      }

      const status = await getMigrationStatus();

      return {
         success: true,
         data: status,
         message: "Migration status retrieved successfully",
      };
   } catch (error) {
      console.error("Error getting migration status:", error);
      const portfolioError = categorizeError(error);
      return {
         success: false,
         error: portfolioError.message,
      };
   }
}
