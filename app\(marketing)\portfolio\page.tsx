"use client";

import ElementReveal from "@/components/animations/element-reveal";
import TextReveal from "@/components/animations/text-reveal";
import {
   ErrorBoundary,
   PortfolioErrorBoundary,
} from "@/components/error-boundary";
import { ImageGallery } from "@/components/image-gallery";
import {
   ErrorState,
   LoadingSpinner,
   PortfolioEmptyState,
   PortfolioLoading,
} from "@/components/loading-states";
import { FilterButton } from "@/components/portfolio-filter-button";
import { Button } from "@/components/ui/button";
import {
   useActivePortfolioServicesWithCoverImages,
   usePortfolioImages,
} from "@/lib/hooks/use-portfolio";
import Lenis from "@studio-freight/lenis";
import { AlertTriangle, ArrowRight } from "lucide-react";
import Link from "next/link";
import { useEffect, useState } from "react";

export default function PortfolioPage() {
   const [activeServiceId, setActiveServiceId] = useState("all");

   // Fetch portfolio services with cover images and all images from database
   const {
      data: servicesWithCoverImages,
      isLoading: servicesLoading,
      error: servicesError,
      refetch: refetchServices,
   } = useActivePortfolioServicesWithCoverImages();
   const {
      data: imagesData,
      isLoading: imagesLoading,
      error: imagesError,
      refetch: refetchImages,
   } = usePortfolioImages();

   const services = servicesWithCoverImages || [];
   const images = imagesData?.data || [];

   // Combined loading state
   const isLoading = servicesLoading || imagesLoading;

   // Combined error state
   const hasError = servicesError || imagesError;

   // Check if we have any data at all
   const hasServices = services.length > 0;
   const hasImages = images.length > 0;

   // Retry function for error recovery
   const handleRetry = () => {
      refetchServices();
      refetchImages();
   };

   useEffect(() => {
      const lenis = new Lenis();

      function raf(time: number) {
         lenis.raf(time);
         requestAnimationFrame(raf);
      }

      requestAnimationFrame(raf);
   });

   const getFilteredImages = () => {
      let filteredImages;

      if (activeServiceId === "all") {
         // Show images from all services
         filteredImages = images;
      } else {
         // Filter images by specific service using serviceId
         filteredImages = images.filter(
            (img) => img.serviceId === activeServiceId
         );
      }

      // Map to gallery format and include display order and creation date for sorting
      return filteredImages.map((img) => ({
         src: img.url,
         alt: img.altText,
         category: services.find((s) => s._id === img.serviceId)?.name || "",
         width: img.width,
         height: img.height,
         displayOrder: img.displayOrder,
         createdAt: img.createdAt,
      }));
   };

   const filteredImages = getFilteredImages();

   console.log("Service: ", services);

   // Create categories array with "all" option plus services
   const categories = [
      { id: "all", name: "All Work", coverImage: null },
      ...services.map((service) => ({
         id: service._id as string,
         name: service.name,
         coverImage: service.coverImageUrl
            ? {
                 url: service.coverImageUrl,
                 altText: service.name, // Use service name as alt text
              }
            : null,
      })),
   ];

   console.log("Filtered Images: ", filteredImages);

   return (
      <ErrorBoundary
         onError={(error, errorInfo) => {
            console.error("Portfolio page error:", error, errorInfo);
         }}
      >
         <div className="min-h-screen">
            {/* Hero Section */}
            <section className="pt-36 pb-16 bg-gradient-hero">
               <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
                  <TextReveal>
                     <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-18">
                        Our <span className="text-primary">Portfolio</span>
                     </h1>
                  </TextReveal>
                  <TextReveal className="mb-8">
                     <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
                        Explore our collection of beautiful photography and
                        videography work. Each image tells a story, captures an
                        emotion, and preserves a precious moment in time.
                     </p>
                  </TextReveal>
                  <ElementReveal>
                     <Button asChild size="lg">
                        <Link href="/contact">Start Your Project</Link>
                     </Button>
                  </ElementReveal>
               </div>
            </section>

            {/* Filter Categories */}
            <section className="py-12 bg-astral-grey">
               <div className="container mx-auto px-4 sm:px-6 lg:px-8">
                  <PortfolioErrorBoundary
                     title="Filter Error"
                     description="There was an error loading the portfolio categories."
                     onRetry={handleRetry}
                  >
                     {servicesLoading ? (
                        <PortfolioLoading type="services" count={5} />
                     ) : servicesError ? (
                        <ErrorState
                           title="Failed to Load Categories"
                           description="We couldn't load the portfolio categories. Please try again."
                           onRetry={handleRetry}
                        />
                     ) : !hasServices ? (
                        <PortfolioEmptyState type="services" />
                     ) : (
                        <div className="flex flex-wrap justify-center gap-4">
                           {categories.map((category) => (
                              <FilterButton
                                 key={category.id}
                                 id={category.id}
                                 name={category.name}
                                 isActive={activeServiceId === category.id}
                                 onClick={() => setActiveServiceId(category.id)}
                                 coverImage={category.coverImage}
                              />
                           ))}
                        </div>
                     )}
                  </PortfolioErrorBoundary>
               </div>
            </section>

            {/* Gallery */}
            <section className="py-20 pt-12">
               <div className="container mx-auto px-4 sm:px-6 lg:px-8">
                  <div className="text-center mb-12">
                     <TextReveal>
                        <h2 className="text-2xl font-bold mb-4">
                           {categories.find((cat) => cat.id === activeServiceId)
                              ?.name || "All Work"}
                        </h2>
                     </TextReveal>
                     <TextReveal>
                        <p className="text-muted-foreground">
                           {isLoading ? (
                              <span className="flex items-center justify-center gap-2">
                                 <LoadingSpinner size="sm" />
                                 Loading...
                              </span>
                           ) : hasError ? (
                              <span className="flex items-center justify-center gap-2 text-red-500">
                                 <AlertTriangle className="w-4 h-4" />
                                 Error loading content
                              </span>
                           ) : (
                              <>
                                 {filteredImages.length}{" "}
                                 {filteredImages.length === 1
                                    ? "image"
                                    : "images"}{" "}
                                 in this category
                              </>
                           )}
                        </p>
                     </TextReveal>
                  </div>

                  <PortfolioErrorBoundary
                     title="Gallery Error"
                     description="There was an error loading the portfolio gallery."
                     onRetry={handleRetry}
                  >
                     {imagesLoading ? (
                        <PortfolioLoading type="gallery" />
                     ) : imagesError ? (
                        <ErrorState
                           title="Failed to Load Images"
                           description="We couldn't load the portfolio images. Please check your connection and try again."
                           onRetry={handleRetry}
                        />
                     ) : !hasImages ? (
                        <PortfolioEmptyState type="images" />
                     ) : filteredImages.length === 0 ? (
                        <PortfolioEmptyState
                           type="category"
                           categoryName={
                              categories.find(
                                 (cat) => cat.id === activeServiceId
                              )?.name
                           }
                        />
                     ) : (
                        <ImageGallery
                           images={filteredImages}
                           sortByDisplayOrder={true}
                        />
                     )}
                  </PortfolioErrorBoundary>
               </div>
            </section>

            {/* CTA Section */}
            <section className="py-20 bg-astral-grey">
               <div className="container mx-auto px-4 text-center">
                  <div className="max-w-3xl mx-auto">
                     <TextReveal>
                        <h2 className="text-4xl font-playfair font-bold text-foreground mb-6">
                           Let&apos;s Create Your{" "}
                           <span className="bg-gradient-accent bg-clip-text text-transparent">
                              Next Masterpiece{" "}
                           </span>
                        </h2>
                     </TextReveal>
                     <TextReveal className="mb-8">
                        <p className="text-lg text-muted-foreground font-montserrat leading-relaxed">
                           Let&apos;s create something beautiful together.
                           Contact us to discuss your photography and
                           videography needs.
                        </p>
                     </TextReveal>
                     <ElementReveal>
                        <div className="flex flex-col sm:flex-row gap-4 items-center justify-center">
                           <Button
                              asChild
                              size="lg"
                              className="bg-gradient-accent hover:opacity-90 font-montserrat font-semibold text-lg px-8"
                           >
                              <Link href="/contact">
                                 Contact Now{" "}
                                 <ArrowRight className="ml-2 h-5 w-5" />
                              </Link>
                           </Button>
                           <Button
                              asChild
                              variant="outline"
                              size="lg"
                              className="border-primary/20 hover:bg-primary/5 font-montserrat font-semibold text-lg px-8"
                           >
                              <Link href="/services">View Services</Link>
                           </Button>
                        </div>
                     </ElementReveal>
                  </div>
               </div>
            </section>
         </div>
      </ErrorBoundary>
   );
}
