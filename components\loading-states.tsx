"use client";

import { cn } from "@/lib/utils";
import { Grid3X3, Image as ImageIcon, Loader2 } from "lucide-react";

interface LoadingSpinnerProps {
   size?: "sm" | "md" | "lg";
   className?: string;
}

export function LoadingSpinner({
   size = "md",
   className,
}: LoadingSpinnerProps) {
   const sizeClasses = {
      sm: "w-4 h-4",
      md: "w-6 h-6",
      lg: "w-8 h-8",
   };

   return (
      <Loader2
         className={cn(
            "animate-spin text-muted-foreground",
            sizeClasses[size],
            className
         )}
      />
   );
}

interface LoadingCardProps {
   className?: string;
}

export function LoadingCard({ className }: LoadingCardProps) {
   return (
      <div className={cn("animate-pulse", className)}>
         <div className="bg-muted rounded-lg h-48 mb-4"></div>
         <div className="space-y-2">
            <div className="bg-muted rounded h-4 w-3/4"></div>
            <div className="bg-muted rounded h-4 w-1/2"></div>
         </div>
      </div>
   );
}

interface PortfolioLoadingProps {
   type?: "services" | "images" | "gallery";
   count?: number;
   className?: string;
}

export function PortfolioLoading({
   type = "gallery",
   count = 6,
   className,
}: PortfolioLoadingProps) {
   if (type === "services") {
      return (
         <div className={cn("flex flex-wrap justify-center gap-4", className)}>
            {Array.from({ length: count }).map((_, index) => (
               <div
                  key={index}
                  className="animate-pulse bg-muted rounded-lg h-12 w-32"
               />
            ))}
         </div>
      );
   }

   if (type === "images") {
      return (
         <div
            className={cn(
               "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",
               className
            )}
         >
            {Array.from({ length: count }).map((_, index) => (
               <LoadingCard key={index} />
            ))}
         </div>
      );
   }

   // Default gallery loading
   return (
      <div
         className={cn(
            "flex flex-col items-center justify-center py-12",
            className
         )}
      >
         <div className="flex items-center gap-3 text-muted-foreground">
            <LoadingSpinner size="lg" />
            <span className="text-lg">Loading portfolio...</span>
         </div>
      </div>
   );
}

interface EmptyStateProps {
   icon?: React.ReactNode;
   title: string;
   description: string;
   action?: React.ReactNode;
   className?: string;
}

export function EmptyState({
   icon,
   title,
   description,
   action,
   className,
}: EmptyStateProps) {
   return (
      <div
         className={cn(
            "flex flex-col items-center justify-center py-12 text-center",
            className
         )}
      >
         <div className="mb-4 text-muted-foreground">
            {icon || <ImageIcon className="w-12 h-12" />}
         </div>
         <h3 className="text-xl font-semibold text-foreground mb-2">{title}</h3>
         <p className="text-muted-foreground mb-6 max-w-md">{description}</p>
         {action}
      </div>
   );
}

interface PortfolioEmptyStateProps {
   type: "services" | "images" | "category";
   categoryName?: string;
   className?: string;
}

export function PortfolioEmptyState({
   type,
   categoryName,
   className,
}: PortfolioEmptyStateProps) {
   if (type === "services") {
      return (
         <EmptyState
            icon={<Grid3X3 className="w-12 h-12" />}
            title="No Portfolio Services"
            description="No portfolio services have been created yet. Please check back later or contact us for more information."
            className={className}
         />
      );
   }

   if (type === "category" && categoryName) {
      return (
         <EmptyState
            icon={<ImageIcon className="w-12 h-12" />}
            title={`No Images in ${categoryName}`}
            description={`There are currently no images in the ${categoryName} category. Please check other categories or visit again later.`}
            className={className}
         />
      );
   }

   // Default images empty state
   return (
      <EmptyState
         icon={<ImageIcon className="w-12 h-12" />}
         title="No Images Found"
         description="No portfolio images are currently available. Please check back later as we regularly update our portfolio."
         className={className}
      />
   );
}

interface ErrorStateProps {
   title?: string;
   description?: string;
   onRetry?: () => void;
   className?: string;
}

export function ErrorState({
   title = "Something went wrong",
   description = "We encountered an error while loading the content. Please try again.",
   onRetry,
   className,
}: ErrorStateProps) {
   return (
      <div
         className={cn(
            "flex flex-col items-center justify-center py-12 text-center",
            className
         )}
      >
         <div className="w-12 h-12 rounded-full bg-red-100 dark:bg-red-900/20 flex items-center justify-center mb-4">
            <svg
               className="w-6 h-6 text-red-600 dark:text-red-400"
               fill="none"
               stroke="currentColor"
               viewBox="0 0 24 24"
            >
               <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
               />
            </svg>
         </div>
         <h3 className="text-xl font-semibold text-foreground mb-2">{title}</h3>
         <p className="text-muted-foreground mb-6 max-w-md">{description}</p>
         {onRetry && (
            <button
               onClick={onRetry}
               className="inline-flex items-center gap-2 px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
            >
               <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
               >
                  <path
                     strokeLinecap="round"
                     strokeLinejoin="round"
                     strokeWidth={2}
                     d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                  />
               </svg>
               Try Again
            </button>
         )}
      </div>
   );
}
