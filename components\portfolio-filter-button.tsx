"use client";

import { Button } from "@/components/ui/button";
import { motion } from "motion/react";
import Image from "next/image";

interface FilterButtonProps {
   id: string;
   name: string;
   isActive: boolean;
   onClick: () => void;
   coverImage?: {
      url: string;
      altText: string;
   } | null;
}

export function FilterButton({
   name,
   isActive,
   onClick,
   coverImage,
}: FilterButtonProps) {
   return (
      <motion.div
         initial={{ opacity: 0, y: 20 }}
         whileInView={{ opacity: 1, y: 0 }}
         transition={{ duration: 0.3 }}
         viewport={{ once: true }}
         className="relative overflow-hidden rounded-lg"
      >
         <Button
            size="lg"
            variant={isActive ? "default" : "outline"}
            onClick={onClick}
            className={`
               relative h-16 px-6 overflow-hidden transition-all duration-300
               ${
                  isActive
                     ? "bg-gradient-accent text-white border-transparent shadow-lg"
                     : "bg-white/90 backdrop-blur-sm border-gray-200 hover:bg-white/95"
               }
               ${coverImage ? "text-white" : ""}
            `}
         >
            {/* Background Image */}
            {coverImage && (
               <>
                  <div className="absolute inset-0 z-0">
                     <Image
                        src={coverImage.url}
                        alt={coverImage.altText}
                        fill
                        className="object-cover"
                        sizes="(max-width: 768px) 200px, 250px"
                     />
                  </div>
                  {/* Overlay for text readability */}
                  <div
                     className={`
                        absolute inset-0 z-10 transition-all duration-300
                        ${
                           isActive
                              ? "bg-gradient-to-r from-primary/80 to-accent/80"
                              : "bg-black/40 hover:bg-black/30"
                        }
                     `}
                  />
               </>
            )}

            {/* Button Text */}
            <span
               className={`
                  relative z-20 font-semibold transition-all duration-300
                  ${
                     coverImage
                        ? "text-white drop-shadow-lg"
                        : isActive
                        ? "text-white"
                        : "text-gray-700"
                  }
               `}
            >
               {name}
            </span>
         </Button>
      </motion.div>
   );
}
