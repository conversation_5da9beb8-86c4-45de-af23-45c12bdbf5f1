# Implementation Plan

-  [x] 1. Create portfolio migration service

   -  Implement `lib/services/portfolio-migration-service.ts` with functions to read static portfolio data and map to database format
   -  Create `migratePortfolioData()` function that handles the complete migration process
   -  Add `checkExistingData()` function to detect if migration has already been run
   -  Implement `mapCategoriesToServices()` and `mapImagesToDatabase()` helper functions
   -  _Requirements: 1.2, 1.3, 2.1, 2.2, 2.5_

-  [x] 2. Create portfolio migration server action

   -  Implement `migratePortfolioDataAction` in `lib/actions/portfolio-actions.ts`
   -  Add proper error handling and validation for migration operations
   -  Include transaction management to ensure data consistency during migration
   -  Return detailed migration results including counts and any errors
   -  _Requirements: 1.4, 7.1, 7.4_

-

-  [x] 3. Create portfolio migration page

   -  Create `/admin/portfolio/migrate/page.tsx` with simple migration interface
   -  Implement migration button that triggers the migration process
   -  Add progress indicator and results display after migration completion
   -  Include error handling and user feedback for migration operations
   -  _Requirements: 1.1, 1.5, 8.1_

-  [x] 4. Update marketing portfolio page to use database content

   -  Modify `app/(marketing)/portfolio/page.tsx` to use `useActivePortfolioServices` and `usePortfolioImages` hooks
   -  Replace static imports from `data/portfolio.ts` with database queries
   -  Update state management to handle services and images from database
   -  Maintain existing visual appearance and functionality while using dynamic data
   -  _Requirements: 3.1, 3.2, 3.3, 3.4, 6.1, 6.4_

-  [x] 5. Implement service cover image functionality

   -  Update portfolio service hooks to include cover image data (first image of each service)
   -  Modify filter button rendering to display service cover images as backgrounds
   -  Ensure proper text readability over background images with overlay or styling
   -  Handle cases where services have no images with appropriate fallbacks
   -  _Requirements: 4.1, 4.2, 4.3, 4.5_

-  [x] 6. Enhance ImageGallery component with display order support

   -  Update `components/image-gallery.tsx` to accept optional `sortByDisplayOrder` prop
   -  Implement sorting logic that orders images by display order when provided
   -  Add fallback sorting by creation date for images with same display order
   -  Maintain backward compatibility with existing usage of the component
   -  _Requirements: 5.1, 5.2, 5.3, 5.4, 5.5_

-  [x] 7. Update portfolio image filtering logic

   -  Modify image filtering in marketing portfolio page to work with database images
   -  Implement proper service-to-image association using serviceId
   -  Update the "All" category filter to show images from all services
   -  Ensure filtered images maintain proper ordering and display
   -  _Requirements: 3.3, 5.1, 5.5_

-  [x] 8. Add comprehensive error handling

   -  Implement error boundaries and fallback states for marketing portfolio page
   -  Add proper error handling for migration operations with user-friendly messages
   -  Include loading states for database operations and image loading
   -  Handle edge cases like empty database, failed queries, and missing images
   -  _Requirements: 7.1, 7.2, 7.3, 7.4_

-  [x] 9. Implement duplicate detection and handling

   -  Add logic to detect existing portfolio data before migration
   -  Implement skip or update strategy for duplicate services and images
   -  Provide clear feedback about duplicates found during migration
   -  Ensure migration can be run multiple times safely without data corruption
   -  _Requirements: 2.5, 7.1_

-  [x] 10. Add migration result reporting

   -  Implement detailed reporting of migration results including counts of created items
   -  Display any errors or warnings encountered during migration
   -  Show information about skipped duplicates and successful operations
   -  Provide clear success/failure feedback to admin users
   -  _Requirements: 1.5, 7.4_

-  [x] 11. Update portfolio page routing and navigation

   -  Ensure proper routing between portfolio management and migration pages
   -  Add navigation link to migration page from main portfolio admin page
   -  Update admin sidebar if needed to include migration functionality
   -  Test navigation flow and ensure proper breadcrumbs or back navigation
   -  _Requirements: 8.1, 8.2_

-  [x] 12. Test implementation by building application

   -  Ensure all TypeScript types compile without errors
   -  Verify all imports and exports are correctly defined
   -  Test that the application builds successfully with new migration features
   -  Validate that marketing portfolio page works with both static and database content
   -  _Requirements: 6.2, 6.3, 8.4, 8.5_
