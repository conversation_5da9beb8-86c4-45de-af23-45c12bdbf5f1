# Requirements Document

## Introduction

This feature implements a data migration system to transfer existing portfolio data from the static `data/portfolio.ts` file to the MongoDB collections (portfolio-services and portfolio-images), and updates the marketing portfolio page to use the database instead of static data. The migration includes creating a simple admin migration page with a button to perform the data transfer, updating the marketing portfolio page to use database collections, implementing category filtering with service cover images, and adding display order functionality to the ImageGallery component.

## Requirements

### Requirement 1

**User Story:** As an admin, I want a simple migration page in the admin dashboard so that I can migrate existing portfolio data from the static file to the database with a single button click.

#### Acceptance Criteria

1. WHEN an admin accesses `/admin/portfolio/migrate` THEN they SHALL see a migration page with a clear migration button
2. WHEN the migration button is clicked THEN the system SHALL read all data from `data/portfolio.ts` file
3. WHEN the migration runs THEN the system SHALL create portfolio services for each category in the categories array
4. WHEN the migration runs THEN the system SHALL populate portfolio-images collection with all images from portfolioImages object
5. WHEN the migration completes THEN the system SHALL provide feedback on the number of services and images migrated

### Requirement 2

**User Story:** As a system, I want to properly map static portfolio data to database schema so that all existing data is preserved and correctly structured.

#### Acceptance Criteria

1. WHEN mapping categories to services THEN each category SHALL become a portfolio service with name, slug, and display order
2. WHEN mapping images to database THEN each image SHALL be associated with the correct service using category mapping
3. WHEN creating services THEN the system SHALL assign display order based on the order in the categories array
4. WHEN creating images THEN the system SHALL generate appropriate alt text following the existing pattern
5. WHEN migration runs multiple times THEN the system SHALL handle duplicate data appropriately (skip or update)

### Requirement 3

**User Story:** As a visitor, I want the marketing portfolio page to use dynamic database content so that the portfolio reflects the current managed content.

#### Acceptance Criteria

1. WHEN the marketing portfolio page loads THEN it SHALL fetch portfolio services from the database instead of static data
2. WHEN portfolio services are displayed THEN they SHALL be sorted by display order
3. WHEN portfolio images are fetched THEN they SHALL come from the portfolio-images collection grouped by service
4. WHEN the page renders THEN it SHALL maintain the same visual appearance and functionality as before
5. WHEN no database content exists THEN the system SHALL handle the empty state gracefully

### Requirement 4

**User Story:** As a visitor, I want portfolio category filters to show service cover images so that I can visually identify different photography services.

#### Acceptance Criteria

1. WHEN portfolio filters are displayed THEN each filter button SHALL show the cover image of the corresponding service in the background
2. WHEN a service has images THEN the system SHALL use the first image as the cover image for the filter button
3. WHEN a service has no images THEN the filter button SHALL display with a default background or placeholder
4. WHEN filter buttons are rendered THEN they SHALL maintain proper text readability over the background images
5. WHEN a filter is selected THEN the background image SHALL provide visual feedback for the active state

### Requirement 5

**User Story:** As a visitor, I want portfolio images to be displayed in the correct order so that the most important images appear first.

#### Acceptance Criteria

1. WHEN the ImageGallery component receives portfolio images THEN it SHALL support an optional display order prop
2. WHEN display order is provided THEN images SHALL be sorted by the display order value
3. WHEN no display order is specified THEN images SHALL maintain their current default ordering
4. WHEN images have the same display order THEN they SHALL be sorted by creation date as a secondary sort
5. WHEN the ImageGallery renders THEN it SHALL maintain all existing functionality while respecting the new ordering

### Requirement 6

**User Story:** As a developer, I want the portfolio.ts file to become redundant so that all portfolio data comes from the database while preserving the file for reference.

#### Acceptance Criteria

1. WHEN the migration is complete THEN the marketing portfolio page SHALL not import or use data from `data/portfolio.ts`
2. WHEN portfolio functionality is tested THEN all features SHALL work without the static data file
3. WHEN the static file is present THEN it SHALL not interfere with the database-driven functionality
4. WHEN building the application THEN there SHALL be no unused import warnings related to the portfolio static data
5. WHEN the system runs THEN the `data/portfolio.ts` file SHALL remain in place but unused

### Requirement 7

**User Story:** As a system, I want proper error handling and validation so that the migration and portfolio display are reliable.

#### Acceptance Criteria

1. WHEN the migration encounters errors THEN it SHALL provide clear error messages and not leave the database in an inconsistent state
2. WHEN the marketing portfolio page fails to load database content THEN it SHALL display an appropriate error state
3. WHEN image loading fails THEN the system SHALL handle missing images gracefully
4. WHEN database operations fail THEN the system SHALL log errors and provide user feedback
5. WHEN the migration is attempted on already migrated data THEN it SHALL detect existing data and handle appropriately

### Requirement 8

**User Story:** As a developer, I want the implementation to follow existing patterns so that it integrates seamlessly with the current codebase.

#### Acceptance Criteria

1. WHEN creating the migration page THEN it SHALL follow existing admin page patterns and styling
2. WHEN updating the marketing portfolio page THEN it SHALL use existing hooks and service patterns
3. WHEN implementing database operations THEN they SHALL use the existing portfolio service layer
4. WHEN adding new functionality THEN it SHALL maintain TypeScript type safety throughout
5. WHEN the feature is complete THEN it SHALL not break existing functionality and pass build validation
