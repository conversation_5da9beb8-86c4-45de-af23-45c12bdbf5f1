# Implementation Plan

-  [x] 1. Create portfolio data models and validation

   -  Create TypeScript interfaces for PortfolioService and PortfolioImage with proper typing
   -  Implement validation functions following existing patterns from image and album models
   -  Create metadata generation utilities for portfolio entities
   -  Add alt text generation function with service name pattern
   -  _Requirements: 1.1, 2.1, 2.3_

-  [x] 2. Implement portfolio service layer

   -  Create portfolio-service.ts with CRUD operations for portfolio services
   -  Create service-image relationship management functions
   -  Implement basic service listing and retrieval functions
   -  Add service statistics (image count) functionality
   -  _Requirements: 1.2, 1.3, 1.4_

-  [x] 3. Implement portfolio image service layer

   -  Create portfolio image CRUD operations in portfolio-service.ts
   -  Implement image-to-service association functions
   -  Add bulk operations for portfolio image management
   -  Create functions to handle image metadata with service context
   -  _Requirements: 2.2, 2.4, 4.4_

-  [x] 4. Create portfolio validation schemas

   -  Create Zod schemas for portfolio service creation and updates
   -  Create Zod schemas for portfolio image operations
   -  Add form validation schemas following existing schema patterns
   -  Implement input sanitization and validation rules
   -  _Requirements: 7.3, 8.4_

-  [x] 5. Implement portfolio server actions

   -  Create portfolio-actions.ts with server actions for portfolio service CRUD operations
   -  Implement direct upload integration following existing patterns (generateUploadUrl, saveImageMetadata)
   -  Add portfolio image metadata saving with service association
   -  Create actions for adding existing images to portfolio services
   -  _Requirements: 4.1, 4.2, 5.4_

-  [x] 6. Integrate portfolio actions with React Query hooks

   -  Create React Query hooks that use portfolio server actions directly
   -  Implement optimistic updates and cache invalidation
   -  Add error handling and retry logic following existing hook patterns
   -  Ensure proper TypeScript integration with server actions
   -  _Requirements: 1.2, 1.3, 2.2, 2.4_

-  [x] 7. Extend admin sidebar with portfolio navigation

   -  Add portfolio navigation item to admin-sidebar.tsx
   -  Update navigation items array with portfolio entry
   -  Ensure proper routing and active state handling
   -  Follow existing sidebar patterns and styling
   -  _Requirements: 3.1_

-  [x] 8. Create portfolio management main page

   -  Create /admin/portfolio page component
   -  Implement simple service listing table
   -  Add service creation, editing, and deletion functionality
   -  Create service cards/table rows with click navigation to service detail
   -  _Requirements: 3.2, 3.3_

-  [ ] 9. Create service detail management page

   -  Create /admin/portfolio/[serviceId] page component
   -  Implement service information editing interface
   -  Add image gallery display for the service
   -  Include image upload and management tools
   -  _Requirements: 3.4_

-  [x] 10. Implement portfolio upload component

   -  Create portfolio-upload.tsx based on existing GalleryUpload component
   -  Add service selection dropdown
   -  Implement multi-image direct upload with service association using existing direct upload patterns
   -  Add progress tracking and error handling
   -  _Requirements: 5.1, 5.2, 5.3, 5.5, 5.6_

-  [x] 11. Create portfolio gallery component

   -  Create portfolio-gallery.tsx based on existing MasonryGallery component
   -  Implement custom action menu with download and delete options
   -  Add image preview modal functionality
   -  Include service-specific image management features
   -  _Requirements: 6.1, 6.2, 6.3, 6.4, 6.5_

-

-  [x] 12. Implement service selection dialog for existing images

   -  Create dialog component for adding existing images to services
   -  Integrate with existing masonry gallery 3-dot menu
   -  Add simple service picker dropdown
   -  Implement image association logic from albums, collections, and ungrouped images
   -  _Requirements: 4.3_

-  [x] 13. Create portfolio management hooks

   -  Create React Query hooks for portfolio services (usePortfolioServices, useCreatePortfolioService, etc.)
   -  Implement hooks for portfolio images management
   -  Add hooks for service-image associations
   -  Follow existing hook patterns from use-albums and use-images
   -  _Requirements: 3.3, 3.4_

-

-  [x] 14. Integrate portfolio functionality with existing gallery components

   -  Update existing MasonryGallery 3-dot menu to include "Add to Portfolio Service" option
   -  Implement service selection dialog integration
   -  Add portfolio service association to existing image management workflows
   -  Ensure compatibility with albums, collections, and ungrouped images
   -  _Requirements: 4.3_

-  [x] 15. Implement portfolio service CRUD forms

-  [ ] 15. Implement portfolio service CRUD forms

   -  Create service creation form with validation
   -  Implement service editing form with pre-populated data
   -  Add service deletion confirmation dialog
   -  Include cover image selection functionality
   -  _Requirements: 1.2, 1.3, 1.4_

-  [x] 16. Add portfolio image management features

   -  Implement image reordering within services
   -  Add bulk image operations (move, delete, reorder)
   -  Create image metadata editing functionality
   -  Implement alt text editing with service name pattern
   -  _Requirements: 2.4, 6.4_

-  [x] 17. Create portfolio dashboard statistics

   -  Add portfolio metrics to admin dashboard
   -  Implement portfolio service and image count displays
   -  Create recent portfolio activity tracking
   -  Integrate with existing dashboard stats system
   -  _Requirements: 3.2_

-  [x] 18. Implement error handling and loading states

   -  Add comprehensive error handling for all portfolio operations
   -  Implement loading states for async operations
   -  Create toast notifications for success/error feedback
   -  Add retry mechanisms for failed operations
   -  _Requirements: 7.2, 8.4_

-  [x] 19. Add portfolio data validation and security

   -  Implement server-side validation for all portfolio inputs
   -  Add authentication checks for portfolio operations
   -  Ensure proper data sanitization and XSS prevention
   -  Implement rate limiting for upload operations
   -  _Requirements: 7.1, 7.3, 7.4, 7.5_

-  [x] 20. Test portfolio implementation by building application

   -  Ensure all TypeScript types compile without errors
   -  Verify all imports and exports are correctly defined
   -  Test that the application builds successfully with new portfolio features
   -  Fix any compilation errors or type issues
   -  _Requirements: 8.1, 8.2, 8.3, 8.4, 8.5_
