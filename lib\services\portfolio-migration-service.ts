import { categories, portfolioImages } from "@/data/portfolio";
import {
   CreatePortfolioImageInput,
   CreatePortfolioServiceInput,
   PortfolioImage,
   PortfolioService,
} from "@/lib/models";
import { getCollection } from "@/lib/mongodb/db";
import { createPortfolioService } from "./portfolio-service";

// Static data types from portfolio.ts
interface StaticCategory {
   id: string;
   name: string;
}

interface StaticImage {
   src: string;
   alt: string;
   category: string;
}

// Migration result interface
export interface MigrationResult {
   servicesCreated: number;
   imagesCreated: number;
   servicesUpdated: number;
   imagesUpdated: number;
   duplicatesSkipped: number;
   errors: string[];
   warnings: string[];
   serviceMapping: Map<string, string>; // categoryId -> serviceId
}

// Serializable migration result for client components
export interface SerializableMigrationResult {
   servicesCreated: number;
   imagesCreated: number;
   servicesUpdated: number;
   imagesUpdated: number;
   duplicatesSkipped: number;
   errors: string[];
   warnings: string[];
   serviceMapping: Record<string, string>; // categoryId -> serviceId (serializable object)
}

// Duplicate detection result interface
export interface DuplicateDetectionResult {
   existingServices: PortfolioService[];
   existingImages: PortfolioImage[];
   hasExistingData: boolean;
   duplicateServiceSlugs: string[];
   duplicateImageUrls: string[];
}

// Service mapping for tracking category to service relationships
interface ServiceMapping {
   categoryId: string;
   serviceId: string;
   serviceName: string;
}

/**
 * Check if migration has already been run by looking for existing services
 */
export async function checkExistingData(): Promise<boolean> {
   try {
      const serviceCollection = await getCollection<PortfolioService>(
         "portfolio-services"
      );

      const count = await serviceCollection.countDocuments();
      return count > 0;
   } catch (error) {
      console.error("Error checking existing data:", error);
      return false;
   }
}

/**
 * Comprehensive duplicate detection for services and images
 */
export async function detectDuplicates(
   categories: StaticCategory[],
   portfolioImages: Record<string, StaticImage[]>
): Promise<DuplicateDetectionResult> {
   try {
      const serviceCollection = await getCollection<PortfolioService>(
         "portfolio-services"
      );
      const imageCollection = await getCollection<PortfolioImage>(
         "portfolio-images"
      );

      // Get all existing services and images
      const existingServices = await serviceCollection.find({}).toArray();
      const existingImages = await imageCollection.find({}).toArray();

      // Create lookup maps for efficient duplicate detection
      const existingServiceSlugs = new Set(
         existingServices.map((service) => service.slug).filter(Boolean)
      );
      const existingImageUrls = new Set(existingImages.map((img) => img.url));

      // Check for duplicate service slugs
      const duplicateServiceSlugs: string[] = [];
      categories.forEach((category) => {
         const expectedSlug = category.name.toLowerCase().replace(/\s+/g, "-");
         if (existingServiceSlugs.has(expectedSlug)) {
            duplicateServiceSlugs.push(expectedSlug);
         }
      });

      // Check for duplicate image URLs
      const duplicateImageUrls: string[] = [];
      Object.values(portfolioImages)
         .flat()
         .forEach((image) => {
            if (existingImageUrls.has(image.src)) {
               duplicateImageUrls.push(image.src);
            }
         });

      return {
         existingServices,
         existingImages,
         hasExistingData:
            existingServices.length > 0 || existingImages.length > 0,
         duplicateServiceSlugs,
         duplicateImageUrls,
      };
   } catch (error) {
      console.error("Error detecting duplicates:", error);
      return {
         existingServices: [],
         existingImages: [],
         hasExistingData: false,
         duplicateServiceSlugs: [],
         duplicateImageUrls: [],
      };
   }
}

/**
 * Map static categories to portfolio service creation inputs
 */
export function mapCategoriesToServices(
   categories: StaticCategory[]
): CreatePortfolioServiceInput[] {
   return categories.map((category, index) => ({
      name: category.name,
      description: "", // Empty for migrated data as per design
      isActive: true,
      displayOrder: index, // Based on array position
   }));
}

/**
 * Extract filename from image src path
 */
function extractFilename(src: string): string {
   const parts = src.split("/");
   return parts[parts.length - 1];
}

/**
 * Infer MIME type from file extension
 */
function inferMimeType(filename: string): string {
   const extension = filename.toLowerCase().split(".").pop();

   switch (extension) {
      case "jpg":
      case "jpeg":
         return "image/jpeg";
      case "png":
         return "image/png";
      case "gif":
         return "image/gif";
      case "webp":
         return "image/webp";
      default:
         return "image/jpeg"; // Default fallback
   }
}

/**
 * Map static images to portfolio image creation inputs
 */
export function mapImagesToDatabase(
   images: StaticImage[],
   serviceMap: Map<string, string>
): CreatePortfolioImageInput[] {
   return images.map((image) => {
      const filename = extractFilename(image.src);
      const serviceId = serviceMap.get(image.category);

      if (!serviceId) {
         throw new Error(`No service found for category: ${image.category}`);
      }

      return {
         url: image.src,
         name: filename,
         serviceId: serviceId,
         width: 0, // Default for migrated data as per design
         height: 0, // Default for migrated data as per design
         fileSize: 0, // Default for migrated data as per design
         mimeType: inferMimeType(filename),
         displayOrder: 0, // Default for migrated data
      };
   });
}

/**
 * Create or update portfolio services from static categories with duplicate handling
 */
async function createServicesFromCategories(
   categories: StaticCategory[],
   duplicateDetection: DuplicateDetectionResult,
   skipDuplicates: boolean = true
): Promise<{
   serviceMappings: ServiceMapping[];
   servicesCreated: number;
   servicesUpdated: number;
   duplicatesSkipped: number;
   errors: string[];
   warnings: string[];
}> {
   const serviceCollection = await getCollection<PortfolioService>(
      "portfolio-services"
   );
   const serviceInputs = mapCategoriesToServices(categories);
   const serviceMappings: ServiceMapping[] = [];
   const errors: string[] = [];
   const warnings: string[] = [];
   let servicesCreated = 0;
   let servicesUpdated = 0;
   let duplicatesSkipped = 0;

   for (let i = 0; i < serviceInputs.length; i++) {
      const serviceInput = serviceInputs[i];
      const category = categories[i];

      try {
         // Validate service input before creation
         if (!serviceInput.name || serviceInput.name.trim().length === 0) {
            errors.push(`Category ${category.id} has invalid name`);
            continue;
         }

         // Check if service already exists by name or expected slug
         const expectedSlug = serviceInput.name
            .toLowerCase()
            .replace(/\s+/g, "-");
         const existingService = duplicateDetection.existingServices.find(
            (service) =>
               service.slug === expectedSlug ||
               service.name.toLowerCase() === serviceInput.name.toLowerCase()
         );

         if (existingService) {
            if (skipDuplicates) {
               // Skip duplicate and use existing service
               serviceMappings.push({
                  categoryId: category.id,
                  serviceId: existingService._id!.toString(),
                  serviceName: existingService.name,
               });
               duplicatesSkipped++;
               warnings.push(
                  `Service '${serviceInput.name}' already exists, using existing service`
               );
               console.log(
                  `Skipping duplicate service: ${serviceInput.name} (${category.id})`
               );
               continue;
            } else {
               // Update existing service
               const updateData = {
                  name: serviceInput.name,
                  description: serviceInput.description,
                  displayOrder: serviceInput.displayOrder,
                  isActive: serviceInput.isActive,
                  updatedAt: new Date(),
               };

               await serviceCollection.updateOne(
                  { _id: existingService._id },
                  { $set: updateData }
               );

               serviceMappings.push({
                  categoryId: category.id,
                  serviceId: existingService._id!.toString(),
                  serviceName: serviceInput.name,
               });
               servicesUpdated++;
               console.log(
                  `Updated existing service: ${serviceInput.name} (${category.id})`
               );
               continue;
            }
         }

         // Create new service
         const createdService = await createPortfolioService(serviceInput);

         if (!createdService || !createdService._id) {
            errors.push(
               `Failed to create service for category ${category.id}: No service returned`
            );
            continue;
         }

         serviceMappings.push({
            categoryId: category.id,
            serviceId: createdService._id.toString(),
            serviceName: createdService.name,
         });
         servicesCreated++;

         console.log(
            `Successfully created service: ${createdService.name} (${category.id})`
         );
      } catch (error) {
         const errorMessage =
            error instanceof Error ? error.message : "Unknown error";
         errors.push(
            `Failed to create/update service for category ${category.id}: ${errorMessage}`
         );
         console.error(
            `Error processing service for category ${category.id}:`,
            error
         );
      }
   }

   // If we have errors but some services were processed, log warnings
   if (errors.length > 0) {
      console.warn("Service processing warnings:", errors);

      // If no services were processed at all, throw error
      if (serviceMappings.length === 0) {
         throw new Error(
            `No services could be processed. Errors: ${errors.join(", ")}`
         );
      }
   }

   return {
      serviceMappings,
      servicesCreated,
      servicesUpdated,
      duplicatesSkipped,
      errors,
      warnings,
   };
}

/**
 * Create or update portfolio images from static data with duplicate handling
 */
async function createImagesFromStatic(
   portfolioImages: Record<string, StaticImage[]>,
   serviceMap: Map<string, string>,
   duplicateDetection: DuplicateDetectionResult,
   skipDuplicates: boolean = true
): Promise<{
   imagesCreated: number;
   imagesUpdated: number;
   duplicatesSkipped: number;
   errors: string[];
   warnings: string[];
}> {
   const imageCollection = await getCollection<PortfolioImage>(
      "portfolio-images"
   );
   const serviceCollection = await getCollection<PortfolioService>(
      "portfolio-services"
   );

   // Flatten all images from all categories
   const allImages: StaticImage[] = [];
   Object.entries(portfolioImages).forEach(([category, images]) => {
      if (Array.isArray(images)) {
         allImages.push(...images);
      } else {
         console.warn(`Invalid images data for category ${category}:`, images);
      }
   });

   if (allImages.length === 0) {
      throw new Error("No images found to migrate");
   }

   let imageInputs: CreatePortfolioImageInput[];
   try {
      imageInputs = mapImagesToDatabase(allImages, serviceMap);
   } catch (error) {
      throw new Error(`Failed to map images to database format: ${error}`);
   }

   let imagesCreated = 0;
   let imagesUpdated = 0;
   let duplicatesSkipped = 0;
   const errors: string[] = [];
   const warnings: string[] = [];

   // Create lookup map for existing images
   const existingImageMap = new Map<string, PortfolioImage>();
   duplicateDetection.existingImages.forEach((img) => {
      existingImageMap.set(img.url, img);
   });

   // Process images in batches to avoid overwhelming the database
   const batchSize = 10;
   for (let i = 0; i < imageInputs.length; i += batchSize) {
      const batch = imageInputs.slice(i, i + batchSize);

      for (const imageInput of batch) {
         try {
            // Validate image input
            if (!imageInput.url || !imageInput.name || !imageInput.serviceId) {
               errors.push(
                  `Invalid image data: ${imageInput.name || "unknown"}`
               );
               continue;
            }

            // Verify service exists
            const service = await serviceCollection.findOne({
               _id: imageInput.serviceId,
            });

            if (!service) {
               errors.push(
                  `Service not found for image ${imageInput.name}: ${imageInput.serviceId}`
               );
               continue;
            }

            // Check if image already exists using our lookup map
            const existingImage = existingImageMap.get(imageInput.url);

            if (existingImage) {
               if (skipDuplicates) {
                  // Skip duplicate
                  duplicatesSkipped++;
                  warnings.push(
                     `Image '${imageInput.name}' already exists, skipping`
                  );
                  console.log(`Skipping duplicate image: ${imageInput.name}`);
                  continue;
               } else {
                  // Update existing image
                  const updateData = {
                     name: imageInput.name,
                     altText: `${service.name} - ${imageInput.name.replace(
                        /\.[^/.]+$/,
                        ""
                     )}`,
                     serviceId: imageInput.serviceId,
                     width: imageInput.width || existingImage.width,
                     height: imageInput.height || existingImage.height,
                     fileSize: imageInput.fileSize || existingImage.fileSize,
                     mimeType: imageInput.mimeType || existingImage.mimeType,
                     displayOrder:
                        imageInput.displayOrder ?? existingImage.displayOrder,
                     updatedAt: new Date(),
                  };

                  await imageCollection.updateOne(
                     { _id: existingImage._id },
                     { $set: updateData }
                  );

                  imagesUpdated++;
                  console.log(`Updated existing image: ${imageInput.name}`);
                  continue;
               }
            }

            // Create new image document with proper alt text
            const imageDoc = {
               url: imageInput.url,
               name: imageInput.name,
               altText: `${service.name} - ${imageInput.name.replace(
                  /\.[^/.]+$/,
                  ""
               )}`,
               serviceId: imageInput.serviceId,
               width: imageInput.width || 0,
               height: imageInput.height || 0,
               fileSize: imageInput.fileSize || 0,
               mimeType: imageInput.mimeType || "image/jpeg",
               displayOrder: imageInput.displayOrder ?? 0,
               createdAt: new Date(),
            };

            await imageCollection.insertOne(imageDoc);
            imagesCreated++;

            if ((imagesCreated + imagesUpdated) % 5 === 0) {
               console.log(
                  `Processed ${imagesCreated + imagesUpdated} images so far...`
               );
            }
         } catch (error) {
            const errorMessage =
               error instanceof Error ? error.message : "Unknown error";
            errors.push(
               `Failed to process image ${imageInput.name}: ${errorMessage}`
            );
            console.error(`Error processing image ${imageInput.name}:`, error);
         }
      }
   }

   // Log summary
   console.log(
      `Image processing completed: ${imagesCreated} created, ${imagesUpdated} updated, ${duplicatesSkipped} skipped, ${errors.length} errors`
   );

   if (errors.length > 0) {
      console.warn("Image processing errors:", errors);

      // If no images were processed at all, throw error
      if (imagesCreated === 0 && imagesUpdated === 0) {
         throw new Error(
            `No images could be processed. Errors: ${errors
               .slice(0, 5)
               .join(", ")}${
               errors.length > 5 ? ` and ${errors.length - 5} more...` : ""
            }`
         );
      }
   }

   return {
      imagesCreated,
      imagesUpdated,
      duplicatesSkipped,
      errors,
      warnings,
   };
}

/**
 * Main migration function that handles the complete migration process with duplicate detection
 */
export async function migratePortfolioData(
   options: {
      skipDuplicates?: boolean;
      forceUpdate?: boolean;
   } = {}
): Promise<MigrationResult> {
   const { skipDuplicates = true, forceUpdate = false } = options;

   const result: MigrationResult = {
      servicesCreated: 0,
      imagesCreated: 0,
      servicesUpdated: 0,
      imagesUpdated: 0,
      duplicatesSkipped: 0,
      errors: [],
      warnings: [],
      serviceMapping: new Map(),
   };

   try {
      // Validate static data exists
      if (!categories || categories.length === 0) {
         result.errors.push("No categories found in static data to migrate");
         return result;
      }

      if (!portfolioImages || Object.keys(portfolioImages).length === 0) {
         result.errors.push(
            "No portfolio images found in static data to migrate"
         );
         return result;
      }

      // Step 1: Detect existing data and duplicates
      console.log("Detecting existing data and potential duplicates...");
      const duplicateDetection = await detectDuplicates(
         categories,
         portfolioImages
      );

      if (
         duplicateDetection.hasExistingData &&
         !forceUpdate &&
         skipDuplicates
      ) {
         result.warnings.push(
            `Found existing data: ${duplicateDetection.existingServices.length} services, ${duplicateDetection.existingImages.length} images`
         );

         if (duplicateDetection.duplicateServiceSlugs.length > 0) {
            result.warnings.push(
               `Duplicate service slugs detected: ${duplicateDetection.duplicateServiceSlugs.join(
                  ", "
               )}`
            );
         }

         if (duplicateDetection.duplicateImageUrls.length > 0) {
            result.warnings.push(
               `Duplicate image URLs detected: ${duplicateDetection.duplicateImageUrls.length} images`
            );
         }
      }

      // Step 2: Create/update services from categories with duplicate handling
      try {
         console.log("Processing portfolio services...");
         const serviceResult = await createServicesFromCategories(
            categories,
            duplicateDetection,
            skipDuplicates
         );

         result.servicesCreated = serviceResult.servicesCreated;
         result.servicesUpdated = serviceResult.servicesUpdated;
         result.duplicatesSkipped += serviceResult.duplicatesSkipped;
         result.errors.push(...serviceResult.errors);
         result.warnings.push(...serviceResult.warnings);

         // Build service mapping for image creation
         serviceResult.serviceMappings.forEach((mapping) => {
            result.serviceMapping.set(mapping.categoryId, mapping.serviceId);
         });

         if (serviceResult.serviceMappings.length === 0) {
            result.errors.push("No services were processed during migration");
            return result;
         }

         console.log(
            `Service processing completed: ${result.servicesCreated} created, ${result.servicesUpdated} updated, ${serviceResult.duplicatesSkipped} skipped`
         );
      } catch (error) {
         const errorMessage =
            error instanceof Error
               ? error.message
               : "Unknown error processing services";
         result.errors.push(`Failed to process services: ${errorMessage}`);
         return result;
      }

      // Step 3: Create/update images from static data with duplicate handling
      try {
         console.log("Processing portfolio images...");
         const imageResult = await createImagesFromStatic(
            portfolioImages,
            result.serviceMapping,
            duplicateDetection,
            skipDuplicates
         );

         result.imagesCreated = imageResult.imagesCreated;
         result.imagesUpdated = imageResult.imagesUpdated;
         result.duplicatesSkipped += imageResult.duplicatesSkipped;
         result.errors.push(...imageResult.errors);
         result.warnings.push(...imageResult.warnings);

         console.log(
            `Image processing completed: ${result.imagesCreated} created, ${result.imagesUpdated} updated, ${imageResult.duplicatesSkipped} skipped`
         );
      } catch (error) {
         const errorMessage =
            error instanceof Error
               ? error.message
               : "Unknown error processing images";
         result.errors.push(`Failed to process images: ${errorMessage}`);
         // Don't return here - we want to report partial success
      }

      // Step 4: Validate migration results
      const totalProcessed =
         result.servicesCreated +
         result.servicesUpdated +
         result.imagesCreated +
         result.imagesUpdated;

      if (totalProcessed > 0) {
         // Migration was at least partially successful
         if (result.errors.length === 0) {
            console.log(
               `Migration completed successfully: ${result.servicesCreated} services created, ${result.servicesUpdated} services updated, ${result.imagesCreated} images created, ${result.imagesUpdated} images updated, ${result.duplicatesSkipped} duplicates handled`
            );
         } else {
            console.log(
               `Migration completed with warnings: ${totalProcessed} items processed, ${result.errors.length} errors`
            );
         }
      } else if (result.duplicatesSkipped > 0) {
         result.warnings.push(
            `Migration completed - all data already exists. ${result.duplicatesSkipped} duplicates were skipped.`
         );
      } else {
         result.errors.push("Migration completed but no data was processed");
      }
   } catch (error) {
      const errorMessage =
         error instanceof Error
            ? error.message
            : "Unknown error occurred during migration";
      result.errors.push(`Migration failed: ${errorMessage}`);

      // Log detailed error for debugging
      console.error("Migration error details:", error);
   }

   return result;
}

/**
 * Force migration with update strategy - updates existing data instead of skipping
 */
export async function forceMigratePortfolioData(): Promise<MigrationResult> {
   return migratePortfolioData({
      skipDuplicates: false,
      forceUpdate: true,
   });
}

/**
 * Get detailed migration status including existing data counts
 */
export async function getMigrationStatus(): Promise<{
   hasExistingData: boolean;
   existingServicesCount: number;
   existingImagesCount: number;
   duplicateDetection: DuplicateDetectionResult | null;
}> {
   try {
      const serviceCollection = await getCollection<PortfolioService>(
         "portfolio-services"
      );
      const imageCollection = await getCollection<PortfolioImage>(
         "portfolio-images"
      );

      const existingServicesCount = await serviceCollection.countDocuments();
      const existingImagesCount = await imageCollection.countDocuments();
      const hasExistingData =
         existingServicesCount > 0 || existingImagesCount > 0;

      let duplicateDetection: DuplicateDetectionResult | null = null;

      // Only run duplicate detection if we have static data to compare
      if (categories && portfolioImages) {
         duplicateDetection = await detectDuplicates(
            categories,
            portfolioImages
         );
      }

      return {
         hasExistingData,
         existingServicesCount,
         existingImagesCount,
         duplicateDetection,
      };
   } catch (error) {
      console.error("Error getting migration status:", error);
      return {
         hasExistingData: false,
         existingServicesCount: 0,
         existingImagesCount: 0,
         duplicateDetection: null,
      };
   }
}

/**
 * Generate a detailed migration report for export
 */
export function generateMigrationReport(result: MigrationResult): {
   summary: string;
   detailedReport: string;
   timestamp: string;
} {
   const timestamp = new Date().toISOString();
   const totalProcessed =
      result.servicesCreated +
      (result.servicesUpdated || 0) +
      result.imagesCreated +
      (result.imagesUpdated || 0);

   // Generate summary
   const summary = `Portfolio Migration Report - ${new Date().toLocaleDateString()}

MIGRATION SUMMARY:
- Total Items Processed: ${totalProcessed}
- Services Created: ${result.servicesCreated}
- Services Updated: ${result.servicesUpdated || 0}
- Images Created: ${result.imagesCreated}
- Images Updated: ${result.imagesUpdated || 0}
- Duplicates Skipped: ${result.duplicatesSkipped}
- Warnings: ${result.warnings?.length || 0}
- Errors: ${result.errors?.length || 0}

STATUS: ${totalProcessed > 0 ? "SUCCESS" : "FAILED"}`;

   // Generate detailed report
   let detailedReport = summary + "\n\n";

   // Add service mapping details
   if (result.serviceMapping && result.serviceMapping.size > 0) {
      detailedReport += `SERVICE MAPPING:\n`;
      detailedReport += `Successfully mapped ${result.serviceMapping.size} categories to portfolio services\n\n`;
   }

   // Add warnings section
   if (result.warnings && result.warnings.length > 0) {
      detailedReport += `WARNINGS (${result.warnings.length}):\n`;
      result.warnings.forEach((warning, index) => {
         detailedReport += `${index + 1}. ${warning}\n`;
      });
      detailedReport += "\n";
   }

   // Add errors section
   if (result.errors && result.errors.length > 0) {
      detailedReport += `ERRORS (${result.errors.length}):\n`;
      result.errors.forEach((error, index) => {
         detailedReport += `${index + 1}. ${error}\n`;
      });
      detailedReport += "\n";
   }

   // Add duplicate handling info
   if (result.duplicatesSkipped > 0) {
      detailedReport += `DUPLICATE HANDLING:\n`;
      detailedReport += `${result.duplicatesSkipped} duplicate items were safely skipped to preserve existing data.\n`;
      detailedReport += `Use "Force Update" migration to overwrite existing data if needed.\n\n`;
   }

   detailedReport += `Report generated at: ${timestamp}\n`;

   return {
      summary,
      detailedReport,
      timestamp,
   };
}
